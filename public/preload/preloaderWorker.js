// 设置每个分片加载的资源个数
var PRE_LOADER_CHUNKSIZE = 4
// 设置最大重试次数
var PRE_LOADER_MAX_RETRIES = 3
// 设置单个资源请求最大超时（单位毫秒)
var PRE_LOADER_TIMEOUT = 60000

// 监听主线程传入的 manifest 数据
self.addEventListener('message', function(e) {
  var manifest = e.data
  var preloadList = manifest.preload || []
  var excludeList = manifest.exclude || []

  var resourcesToLoad = preloadList;
  // 过滤掉需要排除的资源
  // var resourcesToLoad = []
  // for (var i = 0; i < preloadList.length; i++) {
  //   var url = preloadList[i]
  //   var exclude = false
  //   for (var j = 0; j < excludeList.length; j++) {
  //     if (url === excludeList[j]) {
  //       exclude = true
  //       break
  //     }
  //   }
  //   if (!exclude) {
  //     resourcesToLoad.push(url)
  //   }
  // }

  if (resourcesToLoad.length === 0) {
    // 如果没有需要加载的资源，直接返回成功提示
    self.postMessage({ success: true, results: [] })
    return
  }

  // 开始分片预加载
  var chunks = partitionArray(resourcesToLoad, PRE_LOADER_CHUNKSIZE)
  var overallResults = []

  loadChunksSequentially(chunks, 0, overallResults)
})

// 将数组分割成多个 chunk
function partitionArray(array, size) {
  var result = []
  for (var i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size))
  }
  return result
}

// 依次加载每个分片
function loadChunksSequentially(chunks, chunkIndex, overallResults) {
  if (chunkIndex >= chunks.length) {
    // 所有分片加载完成后，过滤出只包含加载失败的资源，然后返回结果
    var errors = overallResults.filter(function(result) {
      return !result.success
    })
    var successCount = overallResults.length - errors.length
    self.postMessage({ finished: true, errors: errors, success: successCount })
    return
  }

  loadChunk(chunks[chunkIndex], function(err, chunkResults) {
    // 此处不会因单个资源加载失败而中断整个流程
    overallResults = overallResults.concat(chunkResults)
    // 在继续加载下一个分片前添加延迟（此处设置为500毫秒）
    setTimeout(function() {
      loadChunksSequentially(chunks, chunkIndex + 1, overallResults)
    }, 500)
  })
}

// 加载单个分片内的所有资源（并发加载），每个资源都会重试 PRE_LOADER_MAX_RETRIES 次
function loadChunk(chunk, callback) {
  var results = []
  var completed = 0

  for (var i = 0; i < chunk.length; i++) {
    // 使用闭包保存当前 i 的值
    (function(index) {
      loadResourceWithRetries(chunk[index], PRE_LOADER_MAX_RETRIES, function(err, result) {
        // 无论成功失败都会返回 result 对象
        // result 格式示例： { url: 'xxx', success: true, status: 200 } 或 { url: 'xxx', success: false, error: '加载失败 xxx' }
        results[index] = result
        completed++
        if (completed === chunk.length) {
          callback(null, results)
        }
      })
    })(i)
  }
}

// 带重试机制的资源加载，加载失败时重试 N 次
function loadResourceWithRetries(url, retries, callback) {
  loadResource(url, function(err, result) {
    if (err && retries > 0) {
      // 可以在这里添加延迟后重试，比如500毫秒
      setTimeout(function() {
        loadResourceWithRetries(url, retries - 1, callback)
      }, 500)
    } else {
      if (err) {
        // 重试结束后仍然失败，返回错误信息
        callback(null, { url: url, success: false, error: err.message })
      } else {
        callback(null, { url: url, success: true, status: result.status })
      }
    }
  })
}

// 根据资源 URL 发起 XMLHttpRequest 请求加载资源
function loadResource(url, callback) {
  var xhr = new XMLHttpRequest()
  xhr.open('GET', url, true)
  // 设置请求超时
  xhr.timeout = PRE_LOADER_TIMEOUT
  // 统一将资源作为文本进行处理，可根据需求调整
  xhr.onreadystatechange = function() {
    if (xhr.readyState === 4) {
      if (xhr.status >= 200 && xhr.status < 300) {
        callback(null, { url: url, status: xhr.status })
      } else {
        callback(new Error('加载失败 ' + url))
      }
    }
  }
  // 请求超时处理
  xhr.ontimeout = function() {
    callback(new Error('加载超时 ' + url))
  }
  xhr.onerror = function() {
    callback(new Error('加载异常 ' + url))
  }
  xhr.send(null)
}
