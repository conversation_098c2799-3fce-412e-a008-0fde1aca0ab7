/**
 * 预加载器（preloader） 接入示例
 *   <script>
 *     window.__PRELOADER__ENABLELOG = true;
 *     window.__PRELOADER__HOST = 'platform-xxx';
 *     window.__PRELOADER__REMOTES = [
 *       'remote-path',
 *       'http://xxx.com/manifest.json'
 *     ];
 *   </script>
 *   <script src="/preload/preloader.js" async defer></script>
 *
 *   @update 2025-03-06 16:22
 */

;(function () {
  var pathname = window.location.pathname
    .replace('index.html', '')
    .replace(new RegExp('/' + window.__PRELOADER__HOST + '/?'), '/')
  var cwd_parent = window.location.origin + pathname
  var cwd = cwd_parent + window.__PRELOADER__HOST + '/'
  var workerUrl = cwd + 'preload/preloaderWorker.js?timestamp=' + Date.now()
  var cacheValidDays = window.__PRELOADER__CACHE_VALID_DAYS || 7 // 可配置缓存有效期（按天计算，默认7）

  // 预加载 manifest 路径补全
  var preloaderManifestList = window.__PRELOADER__REMOTES.map((item) => {
    return /^http/.test(item) ? item : cwd_parent + item + '/manifest.json'
  })

  // 日志开关，设置为 false 可禁用日志输出
  var isLoggingEnabled = !!window.__PRELOADER__ENABLELOG

  // 利用 requestIdleCallback 在浏览器空闲时执行预加载，忽略本地环境
  if ('requestIdleCallback' in window && !isLocalhost()) {
    requestIdleCallback(function (deadline) {
      startPreloader()
    })
  }

  // 定义启动预加载器的函数
  function startPreloader() {
    ;(async function () {
      var arr = preloaderManifestList
      for (var i = 0; i < arr.length; i++) {
        var url = arr[i]

        try {
          logInfo(
            `[PRE_LOADER - ${getCurrentTime()}] 开始加载资源清单 (${i + 1}/${
              arr.length
            })：${url}`
          )
          var data = await startResourceLoader(url)
          logInfo(
            `[PRE_LOADER - ${getCurrentTime()}] 资源加载完成 (${i + 1}/${
              arr.length
            })，返回数据：${JSON.stringify(data)}`
          )
        } catch (err) {
          logError(
            `[PRE_LOADER - ${getCurrentTime()}] 资源加载失败 (${i + 1}/${
              arr.length
            })`
          )
        }
      }
    })()
  }

  /**
   * 启动资源加载器
   * @param {string} url - manifest 文件的 URL 地址
   * @returns {Promise} 返回加载成功后解析的结果，或在发生错误时拒绝 Promise
   */
  function startResourceLoader(url) {
    var fullUrl = url + '?timestamp=' + Date.now()
    var prefixUrl = url.replace('manifest.json', '')
    // 设置缓存 key
    var cacheKey = 'PRELOADER_' + url.replace(/[:/]/g, '_').toUpperCase()
    var cacheDurationMs = cacheValidDays * 24 * 60 * 60 * 1000

    return new Promise(function (resolve, reject) {
      if (window.Worker) {
        var controller = new AbortController()
        var signal = controller.signal
        var timeoutId = setTimeout(function () {
          controller.abort()
        }, 30000) // 超时设置

        logInfo(`[PRE_LOADER - ${getCurrentTime()}] 正在请求 manifest`)
        fetch(fullUrl, { cache: 'no-cache', signal: signal })
          .then(function (response) {
            clearTimeout(timeoutId)
            if (!response.ok) {
              throw new Error(
                `无法加载 manifest 文件，HTTP 状态码: ${response.status}`
              )
            }
            // 获取 Last-Modified 响应头
            var lastModifiedFromServer = response.headers.get('Last-Modified')
            return response.json().then(function (manifest) {
              return { manifest, lastModifiedFromServer }
            })
          })
          .then(function ({ manifest, lastModifiedFromServer }) {
            // 检查缓存是否存在且未过期（缓存有效期根据 cacheDurationMs 计算）
            var cachedData = {}
            try {
              cachedData = JSON.parse(localStorage.getItem(cacheKey)) || {}
            } catch (e) {
              localStorage.removeItem(cacheKey)
            }
            var cachedLastModified = cachedData.lastModified
            var cacheTime = cachedData.cacheTime
            var currentTime = Date.now()
            // 如果缓存不存在 cacheTime 或缓存已过期，则重新拉取资源
            if (!cacheTime || currentTime - cacheTime > cacheDurationMs) {
              localStorage.removeItem(cacheKey)
              cachedLastModified = ''
            }
            // 如果返回的 last-modified 与本地缓存一致，则跳过预加载
            if (
              lastModifiedFromServer &&
              cachedLastModified === lastModifiedFromServer
            ) {
              resolve({ status: 'skip', lastModified: lastModifiedFromServer })
              return
            }

            logInfo(
              `[PRE_LOADER - ${getCurrentTime()}] manifest 更新或首次加载，开始启动 Worker 处理任务`
            )

            // 启动 Worker 处理 manifest（此处保持原有逻辑）
            var worker = new Worker(workerUrl)
            // 补全 preload 资源地址
            var _manifest = {
              preload: manifest.preload.map((item) => prefixUrl + item),
              exclude: manifest.exclude
            }
            worker.postMessage(_manifest)

            worker.addEventListener('message', function (e) {
              logInfo(
                `[PRE_LOADER - ${getCurrentTime()}] Worker 返回处理结果：${JSON.stringify(
                  e.data
                )}`
              )
              // 预加载成功后更新缓存的 Last-Modified
              if (lastModifiedFromServer) {
                localStorage.setItem(
                  cacheKey,
                  JSON.stringify({
                    lastModified: lastModifiedFromServer,
                    result: e.data,
                    cacheTime: Date.now()
                  })
                )
              }
              resolve(e.data)
              worker.terminate()
            })

            worker.addEventListener('error', function (e) {
              reject(new Error(`Worker 运行过程中出现错误：${e.message}`))
            })
          })
          .catch(function (err) {
            clearTimeout(timeoutId)
            reject(err)
          })
      } else {
        logError(
          `[PRE_LOADER - ${getCurrentTime()}] 当前浏览器环境不支持 Web Worker！`
        )
        reject(new Error('当前浏览器不支持 Web Worker'))
      }
    })
  }

  // 封装日志输出函数：logInfo 和 logError
  function logInfo(...args) {
    if (isLoggingEnabled) {
      console.info(...args)
    }
  }

  function logError(...args) {
    if (isLoggingEnabled) {
      console.error(...args)
    }
  }

  /**
   * 获取当前时间的 ISO 格式字符串
   * @returns {string} 当前时间
   */
  function getCurrentTime() {
    return new Date().toLocaleString()
  }

  /**
   * 判断当前页面是否在本地主机环境（localhost 或 127.0.0.1）
   * @returns {boolean} 如果是本地主机则返回 true，否则返回 false
   */
  function isLocalhost() {
    var hostname = window.location.hostname
    return hostname === 'localhost' || hostname === '127.0.0.1'
  }
})()
