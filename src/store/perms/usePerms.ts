import { usePermsStore } from '@/store/perms';

/**
 * 权限判断
 */
export function usePerms() {
  const store = usePermsStore();
  const buttonPermissionKeys = store.buttonPermissionKeys || [];

  /**
   * 按钮权限判断
   * @param perms
   * @param fallback
   */
  function hasBtnPerms(perms: string, fallback = false) {
    if (!perms) return fallback;

    return buttonPermissionKeys.includes(perms);
  }

  return {
    hasBtnPerms,
  };
}
