import { stringify } from 'querystringify';
import { merge } from 'lodash-es';
// api file
import zyx from './zyx.ts';
import lc from './lc.ts';
import jt from './jt.ts';
import sct from './sct.ts';

export const api = {
  type: {
    nil: '',
    ecps: 'ehs-clnt-platform-service',
    aus: 'atomic-upms-service',
  },
  name: merge(zyx, lc, jt, sct),

  /**
   * 组装请求地址
   * @param serviceType
   * @param apiName
   * @param query
   */
  getUrl(serviceType: string, apiName: string, query?: any): string {
    const url = window.$SYS_CFG.apiBaseURL;
    const paramsStr = query ? `?${stringify(query)}` : '';
    const _apiName = apiName?.indexOf('/') === 0 ? apiName : '/' + apiName;
    const _serviceType = serviceType ? '/' + serviceType : '';

    return `${url}${_serviceType}${_apiName}${paramsStr}`;
  },
};
