import { ACTION } from './constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 通讯录分组数据类型
export interface IAddressGroup {
  id: string;
  groupName: string; // 分组名称
  memberCount: number; // 人员数量
  createTime: string; // 创建时间
  updateTime?: string; // 更新时间
  createUserId: string; // 创建用户ID
  createUserName: string; // 创建用户名称
  remark?: string; // 备注
}

// 分页列表数据类型
export interface IPageItem extends IAddressGroup {
  [key: string]: any;
}
export type IPageListRes = IPageRes<IPageItem>;
