<template>
  <ComDrawerB
    :title="title"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :positive-loading="submitLoading"
    :positiveText="isEdit ? '保存' : '新增'"
    :show-action="true"
    :hide-negative="false"
    @handle-negative="handleCancel"
    @handle-positive="handleSubmit"
    class="!w-[600px]"
  >
    <div v-if="isAddGroup || isEdit" class="group-form-content p-6">
      <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="100px">
        <n-form-item label="分组名称" path="groupName">
          <n-input v-model:value="formData.groupName" placeholder="请输入分组名称" />
        </n-form-item>
      </n-form>
    </div>

    <div v-else class="default-content p-6">
      <n-empty description="暂无内容" />
    </div>
  </ComDrawerB>
</template>

<script setup lang="ts">
import { computed, ref, watch, useAttrs, inject, Ref } from 'vue';
import ComDrawerB from '@/components/drawer/ComDrawerB.vue';
import { ACTION, PROVIDE_KEY } from '../../constant';
import type { IActionData, IAddressGroup } from '../../type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { addGroup, updateGroup } from '../../fetchData';

interface Props {
  title: string;
}

const props = defineProps<Props>();
const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>;
const currentData = ref<IAddressGroup>({} as IAddressGroup);
const [submitLoading, wrapFn] = useAutoLoading(false, 0);
const formRef = ref();

// 分组表单数据
const formData = ref({
  id: '', // 编辑时的分组ID
  groupName: '',
  remark: '',
});

// 表单验证规则
const formRules = {
  groupName: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分组名称长度在1到50个字符', trigger: 'blur' },
  ],
};
const isAddGroup = computed(() => currentAction.value.action === ACTION.ADD);
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);

// 监听currentAction变化，更新当前数据
watch(
  () => currentAction.value,
  (newAction) => {
    if (newAction.data) {
      currentData.value = newAction.data as IAddressGroup;

      // 如果是编辑，填充表单数据
      if (newAction.action === ACTION.EDIT) {
        formData.value.id = currentData.value.id || '';
        formData.value.groupName = currentData.value.groupName || '';
        formData.value.remark = currentData.value.remark || '';
      } else {
        // 新增时清空id
        formData.value.id = '';
      }
    }
  },
  { immediate: true }
);

// 处理取消
function handleCancel() {
  handleClose();
  resetForm();
}

function handleClose() {
  emits('action', { action: ACTION.SEARCH });
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

// 处理提交
async function handleSubmit() {
  const submitPromise = async () => {
    await formRef.value?.validate();

    if (formData.value.id) {
      // 编辑操作 - 带上id
      console.log('编辑分组数据:', formData.value);
      await updateGroup(formData.value);
    } else {
      // 新增操作 - 不需要id
      const { id, ...addData } = formData.value;
      console.log('新增分组数据:', addData);
      await addGroup(addData);
    }

    handleClose();
    resetForm();
  };

  try {
    await wrapFn(submitPromise());
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 重置表单
function resetForm() {
  formData.value = {
    id: '',
    groupName: '',
    remark: '',
  };
  currentData.value = {} as IAddressGroup;
}

// 监听show变化，重置数据
watch(
  () => show.value,
  (newVal) => {
    if (!newVal) {
      submitLoading.value = false;
      resetForm();
    }
  }
);

defineOptions({ name: 'AddressManagementAside' });
</script>

<style scoped lang="scss">
.group-form-content,
.add-member-content {
  .n-form {
    margin-top: 16px;
  }
}

.default-content {
  padding: 40px 0;
  text-align: center;
}
</style>
