<template>
  <n-data-table
    class="h-full com-table-container"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../../constant.ts';
import { createColumns } from './columns.ts';
import { DataTableColumns, NButton } from 'naive-ui';
import { IObj } from '@/types';
import { IPageItem } from '../../type.ts';
import { pageList } from '../../fetchData.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { VNode, ref, toRaw, h, onMounted } from 'vue';

const emits = defineEmits<{
  action: [data: { action: ACTION; data: IObj<any> }];
}>();

const { pagination, updateTotal } = useNaivePagination(getTableData, { pageSize: 20 });
const [loading, search] = useAutoLoading();
const columns = ref<DataTableColumns<IPageItem>>([]);
const tableData = ref<IPageItem[]>([]);
const filterData = ref<IObj<any>>({});

function getTableDataWrap(data: IObj<any>) {
  filterData.value = data;
  getTableData();
}

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData.value,
  };

  search(pageList(params))
    .then((res: any) => {
      tableData.value = res.data.rows || [];
      updateTotal(res.data.total || 0);
    })
    .catch((error) => {
      console.error('获取表格数据失败:', error);
      tableData.value = [];
      updateTotal(0);
    });
}

function setColumns() {
  const baseCols = createColumns();
  columns.value.push(...baseCols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    // 编辑
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    // 删除
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button text-red-500',
          onClick: () => emits('action', { action: ACTION.DELETE, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];

  return useActionDivider(acList);
}

// on created
setColumns();

// 组件挂载时获取数据
onMounted(() => {
  getTableData();
});

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'AddressManagementTable' });
</script>
<style module lang="scss">
.com-table-container {
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
}
</style>
