import { DataTableColumn } from 'naive-ui';

// 创建列配置的函数
export function createColumns(): DataTableColumn[] {
  return [
    {
      title: '分组名称',
      key: 'groupName',
      align: 'center',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '人员数量',
      key: 'memberCount',
      align: 'center',
      width: 120,
    },
  ];
}

// 为了向后兼容，导出默认的列配置
export const cols = createColumns();
