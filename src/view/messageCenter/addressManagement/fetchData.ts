import $API from '~/common/api';
import { IObj } from '@/types';
import { IAddressGroup } from './type';

// 通讯录分组列表
export function pageList(params: any) {
  return $API.post({
    url: 'atomic-upms-service/addressGroup/v1/pageList',
    data: params,
  });
}

// 新增通讯录分组
export function addGroup(params: Partial<IAddressGroup>) {
  return $API.post({
    url: 'atomic-upms-service/addressGroup/v1/add',
    data: params,
  });
}

// 编辑通讯录分组
export function updateGroup(params: Partial<IAddressGroup>) {
  return $API.post({
    url: 'atomic-upms-service/addressGroup/v1/update',
    data: params,
  });
}

// 删除通讯录分组
export function deleteGroup(id: string) {
  return $API.post({
    url: 'atomic-upms-service/addressGroup/v1/delete',
    data: { id },
  });
}

// 获取分组详情
export function getGroupDetail(id: string) {
  return $API.get({
    url: 'atomic-upms-service/addressGroup/v1/detail',
    params: { id },
  });
}
