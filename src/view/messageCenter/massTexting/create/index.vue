<template>
  <div class="flex flex-col" style="height: 94%">
    <div class="h-[50px]"></div>
    <div class="create-task-container flex-1">
      <n-scrollbar class="scrollbar-container">
        <div class="content-wrapper p-[20px]">
          <div class="flex justify-between">
            <h2 class="text-lg font-medium">添加发送任务</h2>
            <n-button size="small" type="primary" @click="handleCancel"> 返回 </n-button>
          </div>

          <!-- 表单内容 -->
          <div class="form-container mt-6">
            <n-form
              ref="formRef"
              :model="formData"
              :rules="rules"
              :label-align="'left'"
              :label-placement="'left'"
              :label-width="'80px'"
              :require-mark-placement="'left'"
              class="max-w-4xl"
            >
              <n-form-item label="任务名称" path="taskName" class="mb-6">
                <n-input
                  v-model:value="formData.taskName"
                  placeholder="请输入任务名称，不超过30个字符"
                  :maxlength="30"
                  :show-count="true"
                  class="!w-[400px]"
                />
              </n-form-item>

              <n-form-item label="接收号码" path="recipientType">
                <n-radio-group v-model:value="formData.recipientType">
                  <n-radio value="manual">手动输入接收号码</n-radio>
                  <n-radio value="file">导入接收号码文件</n-radio>
                </n-radio-group>
              </n-form-item>

              <n-form-item v-if="formData.recipientType === 'manual'" label="输入号码" path="manualNumbers">
                <div class="w-full">
                  <div class="flex gap-2 mb-4">
                    <n-button type="primary" size="small" @click="handleAddFromContacts">+ 通讯录中选择 </n-button>
                    <n-button type="primary" size="small" @click="handleAddFromGroup">+ 组织中选择 </n-button>
                  </div>

                  <!-- 已选择的标签 -->
                  <div v-if="selectedContacts.length > 0" class="selected-tags mb-4">
                    <n-tag
                      v-for="contact in selectedContacts"
                      :key="contact.id"
                      :closable="true"
                      @close="removeContact(contact.id)"
                      class="mr-2 mb-2"
                    >
                      {{ contact.name }}（{{ contact.count }}人）
                    </n-tag>
                  </div>

                  <!-- 手动输入区域 -->
                  <div class="manual-input">
                    <div class="text-sm text-gray-600 mb-2">您已选择 {{ totalCount }} 个号码</div>
                    <n-input
                      v-model:value="formData.manualNumbers"
                      type="textarea"
                      placeholder="请手动输入手机号，一行输入一个号码，多个号码请换行分隔。"
                      :rows="8"
                      :maxlength="10000"
                      class="mb-2"
                    />
                    <div class="text-sm text-gray-500">提示：一行输入一个号码，多个号码请换行分隔。</div>
                  </div>
                </div>
              </n-form-item>

              <!-- 导入文件 -->
              <n-form-item v-if="formData.recipientType === 'file'" label="导入文件" path="fileId">
                <div class="w-full">
                  <n-upload :max="1" :file-list="fileList" @change="handleFileChange" accept=".txt,.csv,.xlsx">
                    <n-button type="primary">+ 选择文件</n-button>
                  </n-upload>
                  <div class="text-sm text-gray-500 mt-2">仅支持xlsx格式的Excel文件</div>
                </div>
              </n-form-item>

              <n-form-item label="短信内容" path="content">
                <n-input
                  v-model:value="formData.content"
                  type="textarea"
                  placeholder="请输入短信内容，每条超过66个字符，每66个字算一条信息"
                  :rows="6"
                  :maxlength="1000"
                  :show-count="true"
                />
              </n-form-item>

              <div class="sms-format-tip bg-gray-50 p-4 rounded mb-6 text-sm">
                <div class="font-medium text-gray-700 mb-2">
                  短信格式：【延长石油集团】{{ formData.content || 'xxxxxxxxx短信内容' }}【安全生产智能化管理平台】
                </div>
                <div class="text-gray-600">
                  头部标识内容为"【】"的符号为固定内容，不可修改。短信内容为填写在空内容区，不可超过
                  {{ remainingLength }} 个字符。
                </div>
              </div>
            </n-form>
          </div>

          <div class="action-buttons mt-6 flex gap-4">
            <n-button type="primary" size="large" :loading="submitLoading" @click="handleSubmit"> 发送 </n-button>
            <n-button size="large" @click="handleCancel"> 返回 </n-button>
          </div>
        </div>
      </n-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { FormInst, FormRules, UploadFileInfo } from 'naive-ui';

interface SelectedContact {
  id: string;
  name: string;
  count: number;
}

const router = useRouter();

// 表单引用
const formRef = ref<FormInst | null>(null);

// 表单数据
const formData = ref({
  taskName: '',
  recipientType: 'manual', // 'manual' | 'file'
  manualNumbers: '',
  content: '',
  fileId: '',
});
const selectedContacts = ref<SelectedContact[]>([]);
const fileList = ref<UploadFileInfo[]>([]);
const submitLoading = ref(false);

const totalCount = computed(() => {
  const contactCount = selectedContacts.value.reduce((sum, contact) => sum + contact.count, 0);
  const manualCount = formData.value.manualNumbers.split('\n').filter((line) => line.trim()).length;
  return contactCount + manualCount;
});

const remainingLength = computed(() => {
  const usedLength = formData.value.content.length;
  return Math.max(0, 1000 - usedLength);
});

// 表单验证规则
const rules: FormRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { max: 30, message: '任务名称不能超过30个字符', trigger: 'blur' },
  ],
  recipientType: [{ required: true, message: '请选择接收号码类型', trigger: 'change' }],
  manualNumbers: [
    {
      required: true,
      message: '请输入手机号码或选择联系人',
      trigger: 'blur',
      validator: (_rule: any, value: string) => {
        if (formData.value.recipientType === 'manual') {
          const manualNumbers = value?.split('\n').filter((line) => line.trim()) || [];
          const contactCount = selectedContacts.value.reduce((sum, contact) => sum + contact.count, 0);
          if (manualNumbers.length === 0 && contactCount === 0) {
            return new Error('请输入手机号码或选择联系人');
          }
        }
        return true;
      },
    },
  ],
  fileId: [
    {
      required: true,
      message: '请上传文件',
      trigger: 'change',
      validator: (_rule: any, value: string) => {
        if (formData.value.recipientType === 'file' && !value) {
          return new Error('请上传文件');
        }
        return true;
      },
    },
  ],
  content: [
    { required: true, message: '请输入短信内容', trigger: 'blur' },
    { max: 1000, message: '短信内容不能超过1000个字符', trigger: 'blur' },
  ],
};

// 方法
function handleAddFromContacts() {
  ElMessage.info('打开通讯录选择功能');
  // TODO: 实现通讯录选择功能
  // 注意：实际选择联系人后需要调用 formRef.value?.validate('manualNumbers').catch(() => {});
}

function handleAddFromGroup() {
  ElMessage.info('打开组织选择功能');
  // TODO: 实现组织选择功能
  // 注意：实际选择联系人后需要调用 formRef.value?.validate('manualNumbers').catch(() => {});
}

function removeContact(contactId: string) {
  const index = selectedContacts.value.findIndex((contact) => contact.id === contactId);
  if (index > -1) {
    selectedContacts.value.splice(index, 1);
    // 重新验证输入号码字段，确保实时更新验证状态
    formRef.value?.validate('manualNumbers').catch(() => {});
  }
}

function handleFileChange({ fileList: newFileList }: { fileList: UploadFileInfo[] }) {
  fileList.value = newFileList;
  if (newFileList.length > 0) {
    formData.value.fileId = newFileList[0].id || '';
  } else {
    formData.value.fileId = '';
  }
}

async function handleSubmit() {
  try {
    await formRef.value?.validate();
    submitLoading.value = true;
    // TODO: 实现提交逻辑
    console.log('提交表单数据:', {
      ...formData.value,
      selectedContacts: selectedContacts.value,
      totalCount: totalCount.value,
    });

    // 模拟 API 调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    ElMessage.success('发送任务创建成功');

    router.back();
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitLoading.value = false;
  }
}

function handleCancel() {
  router.back();
}

// 生命周期
onMounted(() => {
  // 页面初始化逻辑
});

defineOptions({ name: 'MassTextingCreate' });
</script>

<style scoped lang="scss">
.create-task-container {
  height: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}

.scrollbar-container {
  height: 100%;
}

.content-wrapper {
  min-height: 100%;
}

.recipient-input {
  width: 100%;
}

.selected-tags {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  min-height: 60px;
  background-color: #fafafa;
}

.manual-input {
  margin-top: 16px;
}

.file-upload {
  margin-top: 16px;
}

.sms-format-tip {
  border-left: 4px solid #2080f0;
}

.action-buttons {
  padding: 16px 20px;
  border-radius: 6px;
}
</style>
