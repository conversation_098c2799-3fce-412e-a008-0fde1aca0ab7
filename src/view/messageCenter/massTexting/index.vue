<template>
  <div class="w-full h-full com-g-row-a1 p-[20px]" ref="captureRef" style="height: 94%">
    <div class="mb-4">
      <n-button type="primary" @click="handleAddTask"> 新增发送任务 </n-button>
    </div>

    <listCopm @action="actionFn" ref="listCompRef" />

    <!-- 侧边栏 -->
    <AsideComp v-model:show="isShowAside" :title="actionLabel" @action="actionFn" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, provide, Ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import listCopm from './comp/table/Table.vue';
import AsideComp from './comp/aside/index.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import type { IActionData, IMassTextingTask } from './type';

const router = useRouter();
const captureRef = ref<HTMLElement | null>(null);
const listCompRef = ref();
// 侧边栏状态管理
const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });
const isShowAside = ref(false);
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

// 新增发送任务
function handleAddTask() {
  router.push({ name: 'massTextingCreate' });
}

// 删除确认函数
function confirmDelete(taskData: IMassTextingTask) {
  ElMessageBox.confirm(`确定要删除任务"${taskData.taskName}"吗？删除后无法恢复。`, '删除确认', {
    confirmButtonText: '确认删除',
    cancelButtonText: '取消',
    type: 'warning',
    confirmButtonClass: 'el-button--danger',
  })
    .then(() => {
      // 用户确认删除
      console.log('执行删除操作:', taskData);
      // TODO: 调用删除API
      // 删除成功后刷新表格
    })
    .catch(() => {
      // 用户取消删除
      console.log('取消删除');
    });
}

function handleSearch() {
  listCompRef.value?.getTableData();
}
// 处理action事件
function actionFn(actionData: IActionData) {
  console.log('MassTexting处理操作:', actionData);
  currentAction.value = actionData;

  switch (actionData.action) {
    case ACTION.SEARCH:
      handleSearch();
      break;
    case ACTION.DETAIL:
      console.log('跳转到详情页面:', actionData.data);
      break;
    case ACTION.DELETE:
      confirmDelete(actionData.data as IMassTextingTask);
      break;
    case ACTION.VIEW_FAILED_NUMBERS:
      console.log('22', 22);
      isShowAside.value = true;
      break;
    default:
      console.warn('未知操作类型:', actionData.action);
      break;
  }
}

defineOptions({ name: 'MassTextingIndex' });
</script>

<style scoped lang="scss">
.header-section {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
