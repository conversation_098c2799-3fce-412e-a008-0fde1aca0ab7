import { DataTableColumn, <PERSON><PERSON><PERSON>, NTag, <PERSON><PERSON><PERSON>on } from 'naive-ui';
import { h } from 'vue';
import { SEND_STATUS_LABEL } from '../../constant';

// 创建列配置的函数，接收事件处理函数
export function createColumns(onFailedCountClick?: (row: any) => void): DataTableColumn[] {
  return [
    {
      title: '任务名称',
      key: 'taskName',
      align: 'center',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '创建时间',
      key: 'content',
      align: 'center',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '状态',
      key: 'status',
      align: 'center',
      width: 120,
      ellipsis: {
        tooltip: true,
      },
      render: (row: any) => {
        const statusMap: Record<string, { text: string; type: any }> = {
          '1': { text: SEND_STATUS_LABEL['1'], type: 'info' },
          '2': { text: SEND_STATUS_LABEL['2'], type: 'success' },
          '3': { text: SEND_STATUS_LABEL['3'], type: 'error' },
        };
        const config = statusMap[row.status] || { text: '未知', type: 'default' };
        return h(NTag, { type: config.type, size: 'small' }, { default: () => config.text });
      },
    },
    {
      title: '号码时间',
      key: 'createTime',
      align: 'center',
      width: 180,
    },
    {
      title: '失败号码数量',
      key: 'failedCount',
      align: 'center',
      width: 120,
      render: (row: any) => {
        const failedCount = row.failedCount || 0;
        if (failedCount > 0) {
          return h(
            NButton,
            {
              text: true,
              style: { color: '#ef4444', padding: 0 },
              onClick: () => {
                // 触发失败号码查看事件
                console.log('查看失败号码:', row);
                onFailedCountClick?.(row);
              },
            },
            { default: () => failedCount }
          );
        }
        return h('span', { style: { color: '#ef4444' } }, failedCount);
      },
    },
    {
      title: '备注',
      key: 'sendTime',
      align: 'center',
      width: 180,
    },
  ];
}

// 为了向后兼容，导出默认的列配置
export const cols = createColumns();
