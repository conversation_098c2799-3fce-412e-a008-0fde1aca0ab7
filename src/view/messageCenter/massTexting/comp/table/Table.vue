<template>
  <n-data-table
    class="h-full com-table-container"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../../constant.ts';
import { createColumns } from './columns.ts';
import { DataTableColumns, NButton } from 'naive-ui';
import { IObj } from '@/types';
import { IPageItem } from '../../type.ts';
import { pageList } from '../../fetchData.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { VNode, ref, toRaw, h, onMounted } from 'vue';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData, { pageSize: 20 });

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  tableData.value = [
    {
      id: '1',
      taskName: '任务1',
      content: '内容1',
      targetType: 1,
      targetIds: ['1'],
      sendCount: 100,
      failedCount: 10,
      status: '1',
      createTime: '2024-01-15 10:30:00',
    },
  ];
  // const params = {
  //   pageNo: pagination.page,
  //   pageSize: pagination.pageSize,
  //   ...filterData,
  // };

  // search(pageList(params))
  //   .then((res: any) => {
  //     tableData.value = res.data.rows || [];
  //     updateTotal(res.data.total || 0);
  //   })
  //   .catch((error) => {
  //     console.error('获取表格数据失败:', error);
  //     tableData.value = [];
  //     updateTotal(0);
  //   });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

// 处理失败号码点击事件
function handleFailedCountClick(row: any) {
  emits('action', { action: 'VIEW_FAILED_NUMBERS', data: row });
}

function setColumns() {
  const baseCols = createColumns(handleFailedCountClick);
  columns.value.push(...baseCols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    // 查看详情
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.DETAIL, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DETAIL }
      ),
    ],
    // 删除
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button text-red-500',
          onClick: () => emits('action', { action: ACTION.DELETE, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];

  return useActionDivider(acList);
}

// on created
setColumns();

// 组件挂载时获取数据
onMounted(() => {
  getTableData();
});

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'MassTextingTable' });
</script>

<style module lang="scss">
.com-table-container {
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
}
</style>
