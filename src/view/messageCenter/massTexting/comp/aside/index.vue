<template>
  <ComDrawerB
    :title="title"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :positive-loading="submitLoading"
    :positiveText="'确定'"
    :show-action="true"
    :hide-negative="true"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[600px]"
  >
    <div v-if="isViewFailedNumbers" class="failed-numbers-content p-6">
      <n-data-table
        :columns="failedNumberColumns"
        :data="failedNumbers"
        :pagination="false"
        :bordered="false"
        size="small"
      />
    </div>

    <div v-else class="default-content p-6">
      <n-empty description="暂无内容" />
    </div>
  </ComDrawerB>
</template>

<script setup lang="ts">
import { computed, ref, watch, useAttrs, inject, Ref } from 'vue';
import ComDrawerB from '@/components/drawer/ComDrawerB.vue';
import { ACTION, PROVIDE_KEY } from '../../constant';
import type { IActionData, IMassTextingTask } from '../../type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

interface Props {
  title: string;
}

const props = defineProps<Props>();
const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>;
const isViewFailedNumbers = computed(() => currentAction.value.action === ACTION.VIEW_FAILED_NUMBERS);
const modifyRef = ref();
const [submitLoading, wrapFn] = useAutoLoading(false, 0);

// 当前操作数据

// 失败号码数据（模拟数据）
const failedNumbers = ref([
  { phone: '13800138001', reason: '号码不存在', failTime: '2024-01-15 10:30:00' },
  { phone: '13800138002', reason: '网络超时', failTime: '2024-01-15 10:31:00' },
  { phone: '13800138003', reason: '余额不足', failTime: '2024-01-15 10:32:00' },
]);

// 失败号码表格列定义
const failedNumberColumns = [
  {
    title: '手机号码',
    key: 'phone',
    align: 'center',
  },
  {
    title: '失败原因',
    key: 'reason',
    align: 'center',
  },
  {
    title: '失败时间',
    key: 'failTime',
    align: 'center',
  },
];

function handleSubmit() {
  handleSubmitted();
}

function handleSubmitted() {
  handleClose();
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

watch(
  () => show.value,
  (val) => {
    if (!val) submitLoading.value = false;
  }
);

defineOptions({ name: 'MassTextingAside' });
</script>

<style scoped lang="scss">
.delete-content {
  padding: 20px 0;
}

.failed-numbers-content {
  .n-table {
    margin-top: 16px;
  }
}

.default-content {
  padding: 40px 0;
  text-align: center;
}
</style>
