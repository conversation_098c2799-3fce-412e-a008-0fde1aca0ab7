import { ACTION, SEND_STATUS, TARGET_TYPE } from './constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 群发短信任务数据类型
export interface IMassTextingTask {
  id?: string;
  taskName?: string; // 任务名称
  content?: string; // 短信内容
  targetType?: TARGET_TYPE; // 发送对象类型
  targetIds?: string[]; // 目标ID列表（单位ID或用户ID）
  sendCount?: number; // 发送数量
  failedCount?: number; // 失败号码数量
  status?: SEND_STATUS | string; // 发送状态
  createTime?: string; // 创建时间
  sendTime?: string; // 发送时间
  createUserId?: string; // 创建用户ID
  createUserName?: string; // 创建用户名称
  remark?: string; // 备注
}

// 分页列表数据类型
export interface IPageItem extends IMassTextingTask {
  [key: string]: any;
}
export type IPageListRes = IPageRes<IPageItem>;
