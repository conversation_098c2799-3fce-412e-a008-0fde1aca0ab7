import $API from '~/common/api';
import { IObj } from '@/types';
import { stringify } from 'querystringify';
import { fileDownloader } from '@/view/common/response';
import { IMassTextingTask } from './type';

// 顶部统计栏
export function getTopStatistics(params: any) {
  return $API.get({
    url: 'atomic-upms-service/massTexting/v1/topStatistics',
    params,
  });
}

// 群发短信列表
export function pageList(params: any) {
  return $API.post({
    url: 'atomic-upms-service/massTexting/v1/pageList',
    data: params,
  });
}

// 新增群发短信任务
export function addTask(params: Partial<IMassTextingTask>) {
  return $API.post({
    url: 'atomic-upms-service/massTexting/v1/add',
    data: params,
  });
}

// 编辑群发短信任务
export function updateTask(params: Partial<IMassTextingTask>) {
  return $API.post({
    url: 'atomic-upms-service/massTexting/v1/update',
    data: params,
  });
}

// 删除群发短信任务
export function deleteTask(id: string) {
  return $API.post({
    url: 'atomic-upms-service/massTexting/v1/delete',
    data: { id },
  });
}

// 立即发送
export function sendTask(id: string) {
  return $API.post({
    url: 'atomic-upms-service/massTexting/v1/send',
    data: { id },
  });
}

// 取消发送
export function cancelTask(id: string) {
  return $API.post({
    url: 'atomic-upms-service/massTexting/v1/cancel',
    data: { id },
  });
}

// 重新发送
export function resendTask(id: string) {
  return $API.post({
    url: 'atomic-upms-service/massTexting/v1/resend',
    data: { id },
  });
}

// 获取任务详情
export function getTaskDetail(id: string) {
  return $API.get({
    url: 'atomic-upms-service/massTexting/v1/detail',
    params: { id },
  });
}

// 导出
export function fileExport(params: IObj<any>, filename: string) {
  const url = window.$SYS_CFG.apiBaseURL + '/atomic-upms-service/massTexting/v1/export?' + stringify(params);

  return fileDownloader(url, { filename });
}
