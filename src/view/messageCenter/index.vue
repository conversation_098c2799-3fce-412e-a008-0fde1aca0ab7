<template>
  <div class="w-full pb-0 h-full">
    <ComTabA v-model:value="curTab" :tab-list="tabList" />
    <component :is="viewComp[curTab]" class="tab-content" />
  </div>
</template>

<script setup lang="ts">
import { defineComponent, ref } from 'vue';
import massText from './massTexting/index.vue';
import addressManage from './addressManagement/index.vue';
import ComTabA from '@/components/tab/ComTabA.vue';

const tabList = [
  { label: '群发短信', value: '' },
  { label: '通讯录管理', value: '' },
];

const curTab = ref(tabList[0].label);
const viewComp: Record<string, any> = {
  [tabList[0].label]: massText,
  [tabList[1].label]: addressManage,
};

defineComponent({
  name: 'messageCenterIndex',
});
</script>

<style scoped lang="scss">
.tab-content {
  background-color: #eef7ff;
  padding-top: 15px;
}
</style>
