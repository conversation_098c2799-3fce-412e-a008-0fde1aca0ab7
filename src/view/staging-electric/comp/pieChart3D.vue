<template>
  <div class="chart-container">
    <div class="chart" ref="echartsRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import 'echarts-gl';
import { getPie3D, getParametricEquation } from './echart.ts';
import { ref, shallowRef, onMounted, nextTick, watch } from 'vue';

// const props = defineProps({
//   data: {
//     type: Array,
//     required: true,
//   },
//   internalDiameterRatio: {
//     type: Number,
//     default: 0.6, // 默认缕空,传0实心不缕空
//   },
//   distance: {
//     type: Number,
//     default: 120,
//   },
//   alpha: {
//     type: Number,
//     default: 20, // 倾斜角度
//   },
//   pieHeight: {
//     type: Number,
//     default: 22,
//   },
//   opacity: {
//     type: Number,
//     default: 0.8,
//   },
// });

// const color = ['#2A9CFF', '#FF4069', '#27E89E', '#FFD938'];
// const optionData: any = ref(JSON.parse(JSON.stringify(props.data)));

// let option: any = {};
// const echartRef = shallowRef<HTMLElement>();
// const echart = shallowRef<echarts.ECharts>();
// watch(
//   () => props.data,
//   (newVal) => {
//     optionData.value = JSON.parse(JSON.stringify(props.data));
//     initChart();
//     console.log('val==========', newVal);
//   },
//   { immediate: false, deep: true }
// );

// const setLabel = () => {
//   optionData.value.forEach((item, index) => {
//     item.itemStyle = {
//       color: color[index],
//     };
//     item.label = {
//       normal: {
//         show: false,
//         color: color[index],
//         position: 'right',
//         offset: [0, 3],
//         formatter: ['{d|{d}%}', '————', '{b|{b}}'].join('\n'),
//         rich: {
//           b: {
//             lineHeight: 25,
//             align: 'left',
//             color: color[index],
//           },
//           c: {
//             fontSize: 22,
//             textShadowColor: '#1c90a6',
//             textShadowOffsetX: 0,
//             textShadowOffsetY: 2,
//             textShadowBlur: 5,
//             color: color[index],
//           },
//           d: {
//             color: color[index],
//             align: 'left',
//           },
//         },
//       },
//     };
//     item.labelLine = {
//       normal: {
//         length2: 30,
//         lineStyle: {
//           width: 1,
//           color: color[index],
//         },
//       },
//     };
//   });
// };

// const initChart = () => {
//   echart.value = echarts.init(echartRef.value);

//   option = getPie3D(
//     optionData.value,
//     props.internalDiameterRatio,
//     props.distance,
//     props.alpha,
//     props.pieHeight,
//     props.opacity
//   );

//   echart.value.setOption(option);

//   // option.series.push({
//   //   name: '信用评价',
//   //   backgroundColor: 'transparent',
//   //   type: 'pie',
//   //   label: {
//   //     opacity: 1,
//   //     fontSize: 13,
//   //     lineHeight: 20,
//   //     show: false,
//   //   },
//   //   startAngle: -40,
//   //   clockwise: false,
//   //   data: optionData.value,
//   //   itemStyle: {
//   //     opacity: 0,
//   //   },
//   // });
//   echart.value.setOption(option);

//   bindListen(echart.value);
// };

// const bindListen = (myChart) => {
//   let selectedIndex = '';
//   let hoveredIndex: number | string = -1;

//   myChart.on('mouseover', (params) => {
//     let isSelected;
//     let isHovered;
//     let startRatio;
//     let endRatio;
//     let k;

//     if (hoveredIndex === params.seriesIndex) {
//       // Do nothing
//     } else {
//       if (hoveredIndex !== '' && hoveredIndex !== -1) {
//         isSelected = option.series[hoveredIndex].pieStatus.selected;
//         isHovered = false;
//         startRatio = option.series[hoveredIndex].pieData.startRatio;
//         endRatio = option.series[hoveredIndex].pieData.endRatio;
//         k = option.series[hoveredIndex].pieStatus.k;

//         if (typeof startRatio !== 'undefined' && typeof endRatio !== 'undefined' && typeof k !== 'undefined') {
//           option.series[hoveredIndex].parametricEquation = getParametricEquation(
//             startRatio,
//             endRatio,
//             isSelected,
//             isHovered,
//             k,
//             option.series[hoveredIndex].pieData.value
//           );
//         }

//         option.series[hoveredIndex].pieStatus.hovered = isHovered;
//         hoveredIndex = '';
//       }

//       if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== '信用评价') {
//         isSelected = option.series[params.seriesIndex].pieStatus.selected;
//         isHovered = true;
//         startRatio = option.series[params.seriesIndex].pieData.startRatio;
//         endRatio = option.series[params.seriesIndex].pieData.endRatio;
//         k = option.series[params.seriesIndex].pieStatus.k;

//         if (typeof startRatio !== 'undefined' && typeof endRatio !== 'undefined' && typeof k !== 'undefined') {
//           option.series[params.seriesIndex].parametricEquation = getParametricEquation(
//             startRatio,
//             endRatio,
//             isSelected,
//             isHovered,
//             k,
//             option.series[params.seriesIndex].pieData.value + 60
//           );
//           option.series[params.seriesIndex].pieStatus.hovered = isHovered;
//           hoveredIndex = params.seriesIndex;
//         }
//       }

//       myChart.setOption(option);
//     }
//   });

//   myChart.on('globalout', () => {
//     let isSelected;
//     let isHovered;
//     let startRatio;
//     let endRatio;
//     let k;

//     if (hoveredIndex !== '' && hoveredIndex !== -1) {
//       isSelected = option.series[hoveredIndex].pieStatus.selected;
//       isHovered = false;
//       k = option.series[hoveredIndex].pieStatus.k;
//       startRatio = option.series[hoveredIndex].pieData.startRatio;
//       endRatio = option.series[hoveredIndex].pieData.endRatio;

//       if (typeof startRatio !== 'undefined' && typeof endRatio !== 'undefined' && typeof k !== 'undefined') {
//         option.series[hoveredIndex].parametricEquation = getParametricEquation(
//           startRatio,
//           endRatio,
//           isSelected,
//           isHovered,
//           k,
//           option.series[hoveredIndex].pieData.value
//         );
//       }

//       option.series[hoveredIndex].pieStatus.hovered = isHovered;
//       hoveredIndex = '';
//     }

//     myChart.setOption(option);
//   });
// };

// onMounted(() => {
//   nextTick(() => {
//     setLabel();
//     initChart();
//   });
//   setTimeout(() => {
//     setLabel();
//     initChart();
//   }, 500);
//   window.onresize = changeSize;
// });

// const changeSize = () => {
//   echart.value && echart.value.resize();
// };

const props = defineProps<{
  data: { name: string; value: number; typePercentage: number }[];
}>();

watch(
  () => props.data,
  () => {
    props.data.forEach((item: any) => {});
    initEcharts();
  }
);

const echartsRef = ref(null);

function initEcharts() {
  const chart = echarts.init(echartsRef.value);
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)', // 设置背景色
      borderWidth: 0, // 设置边框宽度
      textStyle: {
        color: '#fff', // 设置文本颜色
      },
      formatter: function (params: any) {
        // console.log('params', params.data.typePercentage);
        let s = `<div style='display:flex;align-items:center '>`;
        let e = '</div>';
        var result = s + params.marker;
        var name = `<span style="margin:0 10px">${params.name}</span>`;
        var value = `<span style="margin:0 10px">${params.value}</span>`;
        var typePercentage = `<span style="margin:0 10pxs">${params.data.typePercentage + '%'}</span>`;
        result += name + value + typePercentage + e;
        return result;
      },
    },
    legend: {
      icon: 'circle',
      top: 'center',
      right: '6%',
      orient: 'vertical',
      itemGap: 12,
      textStyle: {
        color: '#CCCCCC',
        fontSize: 12,
      },
      itemWidth: 7, // 图形宽度
      itemHeight: 7, // 图形高度
      formatter: function (name: string) {
        // console.log('name', name);
        // console.log('props.data', props.data);
        let typeName;
        let typePercentage;
        for (let i = 0; i < props.data.length; i++) {
          if (props.data[i].name === name) {
            typeName = props.data[i].name;
            typePercentage = props.data[i].typePercentage;
          }
        }
        var arr = [typeName, typePercentage + '%'];
        return arr.join('  ');
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['16%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 10,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          normal: {
            color: function (colors: any) {
              var colorList = [
                '#1969E2',
                '#00ECB3',
                '#FFB800',
                '#FF4747',
                '#0ED1C4',
                '#00C2FF',
                '#694FFF',
                '#FF8B38',
                '#46CB1A',
                '#1b96ff',
                '#ff5e19',
                '#9fbf00',
              ];
              return colorList[colors.dataIndex];
            },
          },
        },
        data: props.data,
      },
    ],
  };
  chart.setOption(option, true);
  window.addEventListener('resize', () => {
    if (chart) chart.resize();
  });
}
onMounted(() => {
  initEcharts();
});
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
