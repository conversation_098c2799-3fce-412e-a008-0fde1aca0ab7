/*
 * @Author: xginger <EMAIL>
 * @Date: 2025-05-16 15:07:55
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-05-16 15:47:34
 * @FilePath: \platform\src\view\staging-electric\comp\fetchData.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import $API from '@/common/api';

export function getNoticeList() {
  return $API.get({
    url: 'ehs-clnt-platform-service/workbench/msg/myMessageList',
    params: {
      messageClass: 2,
      pageNo: 1,
      pageSize: 3,
    },
  });
}
export function getMessageList() {
  return $API.get({
    url: 'ehs-clnt-platform-service/workbench/sysVersion/querySysVersion',
  });
}
