<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-05-16 16:47:27
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-07-29 20:05:31
 * @FilePath: \platform\src\view\staging-electric\comp\ToDoTask3.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="tasks">
    <div class="head">
      <div style="display: flex">
        <img style="width: 40px; height: 40px" src="./assets/kanabn.png" />
        <div style="padding-top: 5px; color: black">安全看板</div>
      </div>
      <div>
        <el-date-picker
          v-model="selectedYear"
          type="year"
          placeholder="选择年份"
          format="YYYY"
          value-format="YYYY"
          @change="handleYearChange"
        />
      </div>
    </div>
    <div class="kanban">
      <div class="title">安全作业管理</div>
      <div class="card">
        <li>
          <p>
            <span>{{ obj1.completed }}</span
            >（起）
          </p>
          <p>累计作业</p>
        </li>
        <li>
          <p>
            <span>{{ obj1.applying }}</span
            >（起）
          </p>
          <p>申请中</p>
        </li>
        <li>
          <p>
            <span>{{ obj1.working }}</span
            >（起）
          </p>
          <p>正在作业中</p>
        </li>
      </div>
    </div>
    <div class="echarts">
      <pieChart3D
        ref="pieChart3DEl"
        :data="pieData"
        :internalDiameterRatio="0"
      ></pieChart3D>
    </div>

    <div class="kanban">
      <div class="title">安全人员管理</div>
      <div class="card">
        <li>
          <p>
            <span>{{ obj2.safetyManager }}</span
            >（人）
          </p>
          <p>安全管理人员</p>
        </li>
        <li>
          <p>
            <span>{{ obj2.specialOperation }}</span
            >（人）
          </p>
          <p>特种作业人员</p>
        </li>
        <li>
          <p>{{ obj2.certificateValidity }}</p>
          <p>证书有效性</p>
        </li>
      </div>
    </div>
    <div class="kanban3">
      <div class="title">承包商</div>
      <div class="card">
        <li>
          <p>
            <span>{{ obj3.unitNum }}</span
            >（家）
          </p>
          <p>承包商企业</p>
        </li>
        <li>
          <p>
            <span>{{ obj3.peopleNum }}</span
            >（家）
          </p>
          <p>承包商人员</p>
        </li>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import firea from "@/assets/image/001-a.png"
import fire from "@/assets/image/001.png"
import warna from "@/assets/image/002-a.png"
import warn from "@/assets/image/002.png"
import faulta from "@/assets/image/003-a.png"
import fault from "@/assets/image/003.png"
import riska from "@/assets/image/004-a.png"
import risk from "@/assets/image/004.png"
import $API from "@/common/api"
import pieChart3D from "./pieChart3D.vue"
import { ElMessage } from "element-plus"
import { computed, onMounted, ref } from "vue"
import { useRouter } from "vue-router"
import { useUserInfo } from "@/store"
const currentYear = new Date().getFullYear().toString()
const selectedYear = ref(currentYear) // 默认选中当前年份
const startTime = ref(`${selectedYear.value}-01-01 00:00:00`)
const endTime = ref(`${selectedYear.value}-12-31 23:59:59`)
const handleYearChange = (selectedYear: any) => {
  startTime.value = `${selectedYear}-01-01 00:00:00`
  endTime.value = `${selectedYear}-12-31 23:59:59`
  getData()
  // 可以在这里执行其他逻辑，如请求数据、更新状态等
}
const bgObj = {
  1: risk,
  2: warn,
  3: fault,
  4: fire,
}
const aBgObj = {
  1: riska,
  2: warna,
  3: faulta,
  4: firea,
}
const userInfo = useUserInfo()

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  list: {
    type: Array as any,
    default: () => [],
  },
  info: {
    type: Array as any,
    default: () => [],
  },
  active: {
    type: String,
    default: "1",
  },
  infoActive: {
    type: Number,
    default: 0,
  },
})
const emits = defineEmits(["tabChange"])
const router = useRouter()
const current = computed(() => {
  return props.active
})

const curTabs = ref(props.infoActive)
const workObj = ref({})
const getWork = () => {
  $API
    .get({
      url: "electric-gas-service/homeOverview/statisticsPersonnelType",
      params: {
        unitId: userInfo.value.unitId,
      },
    })
    .then(async (res: any) => {
      console.log(res, "res")
      workObj.value = res.data
    })
}
getWork()
function changeTab(item: any, index: number) {
  console.log(item, "allala")
  curTabs.value = index
  emits("tabChange", item)
}
const goDetail = async (val) => {
  if (val.eventId) {
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: "iot_monitoring",
        userId: userInfo.value.id,
      },
    })

    let url = `${window.$SYS_CFG.parentHostPath}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${val.eventType}&eventId=${val.eventId}&page=/monitor/realTimeMonitorPage`
    window.open(url, "_blank")
  }
}
const obj1: any = ref({})
const obj2: any = ref({})
const obj3: any = ref({})
const pieData = ref([])
function getData() {
  $API
    .post({
      url: "safe-core-service/picture/countType",
      data: {
        unitId: userInfo.value.unitId,
        includeChildren: "1",
        planStartTime: startTime.value,
        planEndTime: endTime.value,
      },
    })
    .then(async (res: any) => {
      pieData.value = res.data.map((item: any) => {
        return {
          name: item.typeName,
          value: item.typeNumber,
          typePercentage: item.typePercentage,
        }
      })
    })
  $API
    .post({
      url: "safe-core-service/picture/application",
      data: {
        unitId: userInfo.value.unitId,
        planStartTime: startTime.value,
        planEndTime: endTime.value,
        includeChildren: "1",
      },
    })
    .then(async (res: any) => {
      obj1.value = res.data
    })
  $API
    .get({
      url: "electric-gas-service/homeOverview/statisticsPersonnelType",
      params: {
        unitId: userInfo.value.unitId,
      },
    })
    .then(async (res: any) => {
      obj2.value = res.data
    })
  $API
    .get({
      url: "electric-gas-service/homeOverview/statisticsContractor",
      params: {
        unitId: userInfo.value.unitId,
      },
    })
    .then(async (res: any) => {
      obj3.value = res.data
    })
}
getData()
const changeType = (val) => {
  // current.value = val.type
  emits("tabChange", val)
}
const getContentText = (val) => {
  //
  if (val.messageContent || val.strongMsgContent)
    return val.messageContent + val.strongMsgContent + "，请控制安全风险。"
  return (
    val.buildingName +
    val.floorName +
    val.deviceAddress +
    val.deviceTypeName +
    "上报了" +
    val.description +
    "请及时前往查看, 请控制安全风险!"
  )
}

async function goMore() {
  if (props.title === "待办任务") {
    router.push({ name: "notifyTask", state: { tab: "1" } })
  } else {
    // router.push({ name: 'notifyTask', state: { tab: '2' } })
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: "iot_monitoring",
        userId: userInfo.value.id,
      },
    })

    let url = `${window.$SYS_CFG.parentHostPath}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${current.value}&page=/monitor/realTimeMonitorPage`
    window.open(url, "_blank")
  }
}

// 读取消息
function readTask(item) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/readMessage`,
      params: {
        messageId: item.messageId,
        messageClass: "2",
      },
    })
    .then((res: any) => {
      if (res && res.code == "success") {
      }
    })
}

function jump(item) {
  readTask(item)
  if (!item.routePath) {
    return
  }
  if (item.messageTitle === "隐患复查") {
    ElMessage.warning("请前往移动端-隐患排查治理-隐患复查中处理")
    return
  }
  let goUrl
  $API
    .post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: item.sysCode,
        userId: userInfo.value.id,
      },
    })
    .then((res: any) => {
      if (res && res.code == "success") {
        if (item.sysCode === "scene_manage") {
          // 现场管理
          const path = item.routePath.split("#")[1]
          // 移除开头的所有/
          const cleanPath = path.replace(/^\/+/, "")
          goUrl =
            item.routePath.split("#")[0] +
            "?token=" +
            res.data.token +
            "&sysCode=" +
            item.sysCode +
            "#/" +
            cleanPath
        } else if (
          item.sysCode === "risk_level" ||
          item.sysCode === "hazard_inves" ||
          item.sysCode === "safe-operation"
        ) {
          // 风险管控
          if (item.routePath.includes("?")) {
            goUrl =
              item.routePath +
              "&token=" +
              res.data.token +
              "&sysCode=" +
              item.sysCode
          } else {
            goUrl =
              item.routePath +
              "?token=" +
              res.data.token +
              "&sysCode=" +
              item.sysCode
          }
        } else {
          goUrl =
            item.routePath +
            "?token=" +
            res.data.token +
            "&sysCode=" +
            item.sysCode
        }

        window.open(goUrl, "_blank")
      }
    })
}

function getImg(item) {
  const type = item.type
  if (current.value == type) return aBgObj[type]
  return bgObj[type]
}

onMounted(() => {
  console.log("🚀 ~ onMounted ~ props.list:", props.list)
})

defineOptions({ name: "HeadTab" })
</script>

<style scoped lang="scss">
.tasks {
  width: 31%;
  height: 656px;
  background: #ffffff;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  border-radius: 10px;
  padding: 20px;
  .head {
    display: flex;
    justify-content: space-between;
    div:nth-child(1) {
      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 700;
      font-size: 18px;
    }
    div:nth-child(2) {
      color: #527cff;
      cursor: pointer;
    }
  }
  .echarts {
    height: 180px;
  }
  .kanban {
    list-style: none;
    margin-top: 20px;
    .title {
      font-weight: 600;
    }
    .card {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      li {
        width: 32.3%;
        height: 82px;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        border: 1px solid #dfe6f2;

        p {
          font-size: 14px;
          margin-left: 20px;
          span {
            font-size: 18px;
          }
        }
        p:nth-child(1) {
          margin-top: 10px;
        }
        p:nth-child(2) {
          margin-top: 5px;
        }
      }
    }
  }

  .kanban2 {
    list-style: none;
    margin-top: 20px;
    .title {
      font-weight: 600;
    }
    .card {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      li {
        width: 24%;
        height: 82px;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        border: 1px solid #dfe6f2;

        p {
          font-size: 14px;
          margin-left: 20px;
          span {
            font-size: 18px;
          }
        }
        p:nth-child(1) {
          margin-top: 10px;
        }
        p:nth-child(2) {
          margin-top: 5px;
        }
      }
    }
  }
  .kanban3 {
    list-style: none;
    margin-top: 20px;
    .title {
      font-weight: 600;
    }
    .card {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      li {
        width: 49%;
        height: 82px;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        border: 1px solid #dfe6f2;

        p {
          font-size: 14px;
          margin-left: 20px;
          span {
            font-size: 18px;
          }
        }
        p:nth-child(1) {
          margin-top: 10px;
        }
        p:nth-child(2) {
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
