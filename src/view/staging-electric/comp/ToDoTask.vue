<template>
  <div class="tasks">
    <n-date-picker
      class="w-[140px] text-center"
      v-model:value="timestamp"
      format="yyyy年M月"
      type="week"
      :actions="null"
      :theme-overrides="themeOverrides"
    >
    </n-date-picker>

    <div class="week-list" style="margin-top: 15px">
      <div class="week-day-item" v-for="item in weekArr" :key="item.date">
        <span class="week-label"> {{ item.label }} </span>
        <p
          class="week-day"
          :class="{ active: item.date === dayjs().format('YYYY-MM-DD') }"
        >
          {{ item.day }}
        </p>
        <div class="tag-box">
          <el-badge
            badge-class="mr-[1px]"
            v-for="tag in item?.tags"
            :key="tag"
            is-dot
            :color="getColor(tag)"
          />
        </div>
      </div>
    </div>
    <n-scrollbar style="height: 395px; margin-top: 20px" class="list flex-1">
      <template v-if="listData.length">
        <div
          class="item"
          v-for="(item, index) in listData"
          :key="index"
          @click="toDetail(item)"
        >
          <div
            class="icon"
            :style="{ backgroundColor: getColor(item.platformMsgType) }"
          ></div>
          <div class="list-content flex-1">
            <div class="list-h">
              <span> {{ item.sysName }}</span>
              <span> {{ item.pushTime }}</span>
            </div>
            <div class="list-info">{{ item.messageContent }}</div>
          </div>
        </div>
      </template>
      <Empty v-else />
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import firea from '@/assets/image/001-a.png';
import { dayjs } from '~/utils/dayjs';
import fire from '@/assets/image/001.png';
import warna from '@/assets/image/002-a.png';
import warn from '@/assets/image/002.png';
import faulta from '@/assets/image/003-a.png';
import Empty from '@/components/empty-electric/index.vue';
import fault from '@/assets/image/003.png';
import riska from '@/assets/image/004-a.png';
import risk from '@/assets/image/004.png';
import $API from '@/common/api';
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useUserInfo } from '@/store';
import defaultPng from '../../staging/assets/default.png';
const timestamp = ref(dayjs().valueOf());
const themeOverrides = {
  itemTextColorCurrent: '#337eff',
  itemColorActive: '#337eff',
  itemColorIncluded: 'rgba(224, 236, 255, 1)',
  peers: {
    Input: {
      borderHover: '1px solid #337eff',
      borderFocus: '1px solid #337eff',
      boxShadowFocus: '0 0 8px 0 rgba(224, 236, 255, 1)',
    },
  },
};
const route = useRouter();
function toDetail(item: any) {
  if (!item.routePath) {
    return;
  }
  console.log(item.sysCode, item.routePath.split('#')[1]);
  if (item.sysCode === 'edu') {
    route.push({
      path: `/ehs-edu${item.routePath.split('#')[1]}`,
    });
  } else if (item.sysCode === 'hazard_inves') {
    if (item.routePath.split('#')[1].split('/')[1] === 'task-management') {
      route.push({
        path: `/task-management/hazard-task-detail`,
        query: {
          id: item.routePath.split('#')[1].split('/')[3],
        },
      });
    } else {
      const queryParams: any = {};
      if (item.routePath.split('#')[1].split('?')[1]) {
        const queryString = item.routePath.split('#')[1].split('?')[1];
        const queryPairs = queryString.split('&');
        queryPairs.forEach((pair) => {
          const [key, value] = pair.split('=');
          queryParams[key] = decodeURIComponent(value);
        });
      }
      route.push({
        path: `${item.routePath.split('#')[1]}`,
        query: {
          id: queryParams.id,
        },
      });
    }
  } else if (item.sysCode === 'risk_level') {
    // alert(`/ehs-risk${item.routePath.split('#')[1]}`)
    console.log(item.routePath.split('#')[1], item.sysCode === 'risk_level');
    route.push({
      path: `/ehs-risk${item.routePath.split('#')[1]}`,
    });
  } else if (item.sysCode === 'emerg_manage') {
    route.push({
      path: `/ehs-emergency-mgr${item.routePath.split('#')[1]}`,
    });
  }
}
function getColor(type?: string | number) {
  switch (type) {
    case 'yh':
    case '4001':
      return '#337EFF';
    case 'jy':
    case '6001':
      return '#06B951';
    case 'fx':
    case '2001':
      return '#FF6A00';
    default: // qt / 7001
      return '#A9B5CA';
  }
}
const query = ref({
  startTime: '',
  endTime: '',
  messageClass: '2',
});
const weekArr = ref<any[]>([]);
const listData = ref<any[]>([]);

const isShow = ref(false);
function getData() {
  $API
    .get({
      url: `/ehs-clnt-platform-service/workbench/msg/queryMessagePageListCcgm`,
      params: { ...query.value },
    })
    .then((res: any) => {
      const { messageResultDtoList, dayTodoList, isEmpty } = res.data || [];

      weekArr.value.map((e: any) => {
        e.tags = dayTodoList[e.date];
        return e;
      });

      listData.value = messageResultDtoList;
      isShow.value = isEmpty;
    });
}
watch(
  () => timestamp.value,
  (val) => {
    query.value.startTime = dayjs(val)
      .startOf('isoWeek')
      .format('YYYY-MM-DD HH:mm:ss');
    query.value.endTime = dayjs(val)
      .endOf('isoWeek')
      .format('YYYY-MM-DD HH:mm:ss');
    console.log(query.value);

    // 一周的每一天
    weekArr.value = Array.from({ length: 7 }, (_, i) => {
      const date = dayjs(query.value.startTime)
        .add(i, 'day')
        .format('YYYY-MM-DD');
      return {
        date,
        day: dayjs(date).format('D'),
        label: getWeekDay(date),
        tags: [],
      };
    });

    getData();
  },
  { immediate: true }
);
function getWeekDay(date: string) {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  return `${week[new Date(date).getDay()]}`;
}
const bgObj = {
  1: risk,
  2: warn,
  3: fault,
  4: fire,
};
const aBgObj = {
  1: riska,
  2: warna,
  3: faulta,
  4: firea,
};
const userInfo = useUserInfo();

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  list: {
    type: Array as any,
    default: () => [],
  },
  info: {
    type: Array as any,
    default: () => [],
  },
  active: {
    type: String,
    default: '1',
  },
  infoActive: {
    type: Number,
    default: 0,
  },
});
const emits = defineEmits(['tabChange']);
const router = useRouter();
const current = computed(() => {
  return props.active;
});

const curTabs = ref(props.infoActive);

function changeTab(item: any, index: number) {
  console.log(item, 'allala');
  curTabs.value = index;
  emits('tabChange', item);
}

const goDetail = async (val) => {
  if (val.eventId) {
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'iot_monitoring',
        userId: userInfo.value.id,
      },
    });

    let url = `${window.$SYS_CFG.parentHostPath}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${val.eventType}&eventId=${val.eventId}&page=/monitor/realTimeMonitorPage`;
    window.open(url, '_blank');
  }
};
const changeType = (val) => {
  // current.value = val.type
  emits('tabChange', val);
};
const getContentText = (val) => {
  //
  if (val.messageContent || val.strongMsgContent)
    return val.messageContent + val.strongMsgContent + '，请控制安全风险。';
  return (
    val.buildingName +
    val.floorName +
    val.deviceAddress +
    val.deviceTypeName +
    '上报了' +
    val.description +
    '请及时前往查看, 请控制安全风险!'
  );
};

async function goMore() {
  if (props.title === '待办任务') {
    router.push({ name: 'notifyTask', state: { tab: '1' } });
  } else {
    // router.push({ name: 'notifyTask', state: { tab: '2' } })
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'iot_monitoring',
        userId: userInfo.value.id,
      },
    });

    let url = `${window.$SYS_CFG.parentHostPath}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${current.value}&page=/monitor/realTimeMonitorPage`;
    window.open(url, '_blank');
  }
}

// 读取消息
function readTask(item) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/readMessage`,
      params: {
        messageId: item.messageId,
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
      }
    });
}

function jump(item) {
  readTask(item);
  if (!item.routePath) {
    return;
  }
  if (item.messageTitle === '隐患复查') {
    ElMessage.warning('请前往移动端-隐患排查治理-隐患复查中处理');
    return;
  }
  let goUrl;
  $API
    .post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: item.sysCode,
        userId: userInfo.value.id,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        if (item.sysCode === 'scene_manage') {
          // 现场管理
          const path = item.routePath.split('#')[1];
          // 移除开头的所有/
          const cleanPath = path.replace(/^\/+/, '');
          goUrl =
            item.routePath.split('#')[0] +
            '?token=' +
            res.data.token +
            '&sysCode=' +
            item.sysCode +
            '#/' +
            cleanPath;
        } else if (
          item.sysCode === 'risk_level' ||
          item.sysCode === 'hazard_inves' ||
          item.sysCode === 'safe-operation'
        ) {
          // 风险管控
          if (item.routePath.includes('?')) {
            goUrl =
              item.routePath +
              '&token=' +
              res.data.token +
              '&sysCode=' +
              item.sysCode;
          } else {
            goUrl =
              item.routePath +
              '?token=' +
              res.data.token +
              '&sysCode=' +
              item.sysCode;
          }
        } else {
          goUrl =
            item.routePath +
            '?token=' +
            res.data.token +
            '&sysCode=' +
            item.sysCode;
        }

        window.open(goUrl, '_blank');
      }
    });
}

function getImg(item) {
  const type = item.type;
  if (current.value == type) return aBgObj[type];
  return bgObj[type];
}

onMounted(() => {
  console.log('🚀 ~ onMounted ~ props.list:', props.list);
});

defineOptions({ name: 'HeadTab' });
</script>

<style scoped lang="scss">
.tasks {
  width: 35%;
  height: 656px;
  background: #ffffff;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  border-radius: 10px;
  padding: 20px;
  .week-list {
    @apply flex items-center justify-between text-center;

    .week-day-item {
      @apply h-full flex flex-col items-center justify-start cursor-default;
      gap: 6px;
      font-size: 18px;

      .week-label {
        @apply text-[#A8A8A8];
        line-height: 25px;
      }

      .week-day {
        @apply font-bold text-center;
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 10px 10px 10px 10px;

        &:hover {
          @apply bg-[#E0ECFF];
        }

        &.active {
          @apply text-[#fff] bg-[#337eff];
        }
      }

      .tag-box {
        @apply flex items-center justify-center;
      }
    }
  }
  .list {
    @apply min-h-0;
    font-size: 14px;

    .item {
      width: 100%;
      height: 80px;
      background: #ffffff;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #dfe6f2;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        border-color: rgba(82, 124, 255, 0.5);
      }

      .icon {
        width: 10px;
        height: 100%;
        border-radius: 10px 0 0 10px;
      }

      .list-content {
        @apply h-full flex-1 flex flex-col justify-between overflow-hidden;
        padding: 15px;

        .list-h {
          @apply flex items-center justify-between;

          span:first-child {
            @apply text-[#222] font-bold;
          }

          span:last-child {
            @apply text-[#666];
            font-size: 12px;
          }
        }

        .list-info {
          @apply w-full text-[#333];
          // 文字溢出省略
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
