<template>
  <div class="top" v-loading="loading">
    <div class="left">
      <div class="img img-text">{{ userInitial }}</div>
      <div>
        <div class="left-name">{{ ui.unitName }}</div>
        <div class="left-dept">
          {{ ui.userName }}
          <span v-if="ui.loginName">/ {{ ui.loginName }}</span>
        </div>
        <div class="left-dept">
          {{ ui.deptName }} <span v-if="ui.postName">/ {{ ui.postName }}</span>
        </div>
        <div class="select_dept">
          <el-select v-model="model" @change="changeDept" placement="right-end">
            <el-option v-for="item in deptList" :key="item.orgCode" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div class="middle">
      <div class="head">
        <div style="display: flex">
          <img style="width: 20px; height: 20px" src="./assets/tongzhi.png" />
          <div style="margin-left: 5px; color: black">通知公告</div>
        </div>
        <div @click="linkHandle()">查看更多</div>
      </div>
      <n-scrollbar style="height: 100px">
        <div class="list" v-if="tableData.length > 0">
          <li v-for="item in tableData" :key="item.id" @click="linkDetail(item)">
            <div class="item-icon"></div>
            <div>{{ item.messageTitle }}</div>
            <div>{{ item.pushTime }}</div>
          </li>
        </div>
        <div class="empty-box" v-else>
          <EmptyComp class="empty-comp" :image-size="185" />
        </div>
      </n-scrollbar>
    </div>
    <div class="right">
      <div class="head">
        <div style="display: flex">
          <img style="width: 20px; height: 20px" src="./assets/tongzhi.png" />
          <div style="margin-left: 5px; color: black">通知公告</div>
        </div>
        <!-- <div>查看更多</div> -->
      </div>
      <n-scrollbar style="height: 100px">
        <div class="list">
          <li v-for="item in tableData2" :key="item.id">
            <div>
              <span :class="item.flag === 1 ? 'active' : ''">{{ item.versionCode }}</span
              ><span :title="item.versionTitle">{{ item.versionTitle }}</span>
            </div>
            <div>{{ item.pushDate }}</div>
          </li>
        </div>
      </n-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import $API from '@/common/api';
import { computed, onMounted, ref } from 'vue';
import EmptyComp from '@/components/empty/empty2.vue';
import { useRouter } from 'vue-router';
import { useUserInfo } from '@/store';
import { getNoticeList, getMessageList } from './fetchData';
import Empty from '@/components/empty-electric/index.vue';
const router = useRouter();
const ui: any = useUserInfo();
const loading = ref<boolean>(false);
const deptList = ref<any>([]);
const model = ref<string>('');
const userInitial = computed(() => {
  const name = ui.value.userName || '';
  return name.length >= 2 ? name.slice(-2) : name;
});
const tableData: any = ref([]);
const tableData2 = ref([]);
function getData() {
  getNoticeList().then((res: any) => {
    if (res.data.rows?.length > 0) {
      tableData.value = res.data.rows;
    }
  });
  getMessageList().then((res: any) => {
    if (res.data?.length > 0) {
      tableData2.value = res.data;
      console.log(tableData2.value, '>>>>>>>.');
    }
  });
}
getData();

function linkDetail(item: any) {
  router.push({
    name: 'noticeDetail',
    query: {
      id: item.id,
      messageId: item.messageId,
    },
  });
}
function linkHandle() {
  router.push({ name: 'notifyTask', state: { tab: '2' } });
}

// 获取部门列表
function getDeptList() {
  $API
    .post({
      url: 'ehs-clnt-platform-service/login/appGetUserAuthUit',
    })
    .then(async (res: any) => {
      deptList.value = res.data;
      deptList.value.forEach((v: any) => {
        v.label = `${v.unitName}-${v.orgName}`;
        v.value = v.orgCode;
      });
      model.value = ui.value.orgCode;
      // changeDept(model.value)
    });
}

// 选择部门
function changeDept(value) {
  $API
    .post({
      url: 'ehs-clnt-platform-service/login/getUnitDetailInfoWithToken',
      params: {
        orgCode: value,
        client: 'platform',
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        const d = res.data;
        d.resourceVoList = res?.data?.resourceVoList.map((item) => {
          return {
            ...item,
            children: item.childrens,
          };
        });
        localStorage.removeItem('@@web_userInfo');
        sessionStorage.removeItem('@@web_userInfo');
        ui.value = d;
        ui.value.resourceList = d?.resourceVoList;
        ui.value.systemName = d?.systemName || '延长石油';
        localStorage.setItem('@@web_userInfo', JSON.stringify(ui.value));
        sessionStorage.setItem('@@web_userInfo', JSON.stringify({ userInfo: { ...ui.value } }));
        router.push('/staging');
        setTimeout(() => {
          window.location.reload();
        }, 300);
      }
    });
}

onMounted(() => {
  getDeptList();
  // getSafeDays();
});
</script>

<style lang="scss" scoped>
.top {
  width: 100%;
  height: 168px;
  // background: url('./assets/bj.png') no-repeat;
  // background-size: 100% 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 33.3%;
    height: 100%;
    // background: linear-gradient(139deg, #e9f1ff 0%, #eaf2ff 0%, #c7d4ff 100%);
    border: 0px solid #ffffff;
    border-radius: 10px;
    display: flex;
    padding-left: 33px;
    align-items: center;
    background: url('@/assets/image/bj.png') no-repeat;
    background-size: 100% 100%;

    .img {
      width: 72px;
      height: 72px;
      object-fit: cover;
      margin-right: 20px;
      border-radius: 50%;
      background-color: #527cff;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .left-name {
      font-weight: 700;
      font-size: 20px;
      color: #222222;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 12px;
    }

    .left-dept {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .select_dept {
      // width: 180px;
      margin-left: -13px;
      // padding-top: 5px;

      :deep(.el-select) {
        min-width: 100px;
        width: auto;

        //max-width: 212px;
        .el-select__wrapper {
          background-color: transparent !important;
          box-shadow: none;
        }

        .el-select__placeholder {
          font-weight: 400;
          font-size: 14px;
          color: #2b2d33;
        }
      }

      :deep(.el-select__caret) {
        color: #00030a;
      }
    }

    .img-text {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      background-color: #527cff;
    }
  }

  .right {
    width: 33.3%;
    height: 100%;
    border-radius: 10px;
    background-color: white;
    // background: url('@/assets/image/wehther.png') no-repeat;
    background-size: 100% 100%;
    padding: 18px 20px 20px 20px;
    .head {
      display: flex;

      justify-content: space-between;
      div:nth-child(1) {
        font-family:
          Alibaba PuHuiTi 2,
          Alibaba PuHuiTi 20;
        font-weight: 700;
        font-size: 18px;
      }
      div:nth-child(2) {
        color: #527cff;
        cursor: pointer;
      }
    }
    .list {
      margin-top: 10px;
      li {
        width: 98%;
        display: flex;
        justify-content: space-between;
        height: 35px;
        div:nth-child(1) {
          font-size: 16px;
          font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 20;
          display: flex;
          font-weight: 500;
          span:nth-child(2) {
            width: 320px;
            display: inline-block;
            text-overflow: ellipsis; /* 使用省略号表示超出部分 */
            white-space: nowrap; /* 强制文本不换行 */
            overflow: hidden; /* 隐藏超出部分 */
          }
          span:nth-child(1) {
            display: inline-block;
            width: 50px;
            height: 22px;
            background: #cfd1d9;
            border-radius: 3px 3px 3px 3px;
            font-family:
              Alibaba PuHuiTi 2,
              Alibaba PuHuiTi 20;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            text-align: center;
            line-height: 22px;
            margin-right: 5px;
          }
          .active {
            display: inline-block;
            width: 50px;
            height: 22px;
            background: #f12b2b;
            border-radius: 3px 3px 3px 3px;
            font-family:
              Alibaba PuHuiTi 2,
              Alibaba PuHuiTi 20;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            text-align: center;
            line-height: 22px;
            margin-right: 5px;
          }
        }
        div:nth-child(2) {
          font-family: D-DIN-PRO, D-DIN-PRO;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
        }
      }
    }
  }

  .middle {
    flex: 1;
    margin: 0 10px;
    // border: 1px solid #300cc1;
    border-radius: 10px;
    // background: url('@/assets/image/wehther.png') no-repeat;
    background-color: white;
    background-size: 100% 100%;
    padding: 18px 20px 20px 20px;

    justify-content: space-between;
    .head {
      display: flex;
      justify-content: space-between;
      div:nth-child(1) {
        font-family:
          Alibaba PuHuiTi 2,
          Alibaba PuHuiTi 20;
        font-weight: 700;
        font-size: 18px;
      }
      div:nth-child(2) {
        color: #527cff;
        cursor: pointer;
      }
    }
    .list {
      margin-top: 10px;
      li {
        cursor: pointer;
        display: flex;
        width: 98%;
        // justify-content: space-between;
        height: 35px;
        position: relative;
        .item-icon {
          width: 26px;
          height: 26px;
          background: url('../assets/icon-notice.png') no-repeat center;
          background-size: 100% 100%;
        }
        div:nth-child(2) {
          font-size: 16px;
          font-family:
            Alibaba PuHuiTi 2,
            Alibaba PuHuiTi 20;
          font-weight: 500;
        }
        div:nth-child(3) {
          font-family: D-DIN-PRO, D-DIN-PRO;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          position: absolute;
          right: 0;
        }
      }
    }
  }
}
.empty-box {
  @apply relative w-[full]  flex justify-center overflow-hidden;
  height: 103px;
  .empty-comp {
    @apply absolute top-[50%];
  }
}
</style>
