<template>
  <div class="tasks">
    <div class="head">
      <div style="display: flex">
        <img style="width: 40px; height: 40px" src="./assets/shishi.png" />
        <div style="padding-top: 5px; color: black">实时监测</div>
      </div>
      <div @click="toPage()">查看更多</div>
    </div>
    <div class="card">
      <div :class="name === '作业中' ? 'active' : ''" @click="handleClick('作业中')">
        <p>作业中焊机</p>
        <p>
          <span style="font-size: 18px">{{ cardObj.runningDevice }}</span
          >（台）
        </p>
      </div>
      <div :class="name === '风险' ? 'active' : ''" @click="handleClick('风险')">
        <p>风险预警</p>
        <p>
          <span style="font-size: 18px">{{ cardObj.warnPre }}</span
          >（台）
        </p>
      </div>
    </div>
    <n-scrollbar style="height: 414px; margin-top: 20px" v-if="tableData.length">
      <div class="list" v-if="name === '作业中'">
        <li v-for="item in tableData" :key="item.id">
          <div class="left">
            <img
              v-if="item.fileUrl !== ''"
              :src="getApiBase() + item.fileUrl"
              style="
                width: 62px;
                height: 68px;

                border-radius: 6px 6px 6px 6px;
              "
            />
            <img
              v-else
              src="./noImg.png"
              style="
                width: 62px;
                height: 68px;

                border-radius: 6px 6px 6px 6px;
              "
            />
          </div>
          <div class="right">
            <p>
              <span>{{ item.operator }}({{ item.phone }})</span>&nbsp;位于<span
                >&nbsp;{{ item.deviceAddress || '--' }}&nbsp;</span
              >正在使用焊机<span>({{ item.deviceNo }})</span>&nbsp;进行作业。
            </p>

            <p>
              <span>开机时间：{{ item.onTime }}</span>
              <span>{{ item.runningTime }}</span>
            </p>
          </div>
        </li>
      </div>
      <div class="list2" v-else>
        <li v-for="item in tableData" :key="item.id">
          <div class="left" v-if="item.warnTypeName === '超长作业'">
            <img :src="getApiBase() + item.fileUrl" style="width: 62px; height: 68px; border-radius: 6px 6px 6px 6px" />
          </div>

          <div class="right">
            <p>{{ item.warnTypeName }}</p>
            <p v-if="item.warnTypeName === '超长作业'">
              <span style="color: #527cff">{{ item.operator }}({{ item.phone }})</span>&nbsp;位于<span
                >&nbsp;{{ item.deviceAddress || '--' }}&nbsp;</span
              ><span style="color: #527cff">({{ item.deviceNo }})</span
              ><span>已经作业超过12小时期，请检查是否存在证件挪用情况（未及时关闭焊机）！</span>
            </p>
            <p v-else>
              <span style="color: #527cff">{{ item.deviceAddress }}</span
              >的焊机<span>&nbsp;{{ item.deviceNo }}</span
              ><span>已经通电超过12小时未进行作业操作，请检查是否关闭设备电源！</span>
            </p>
            <p>
              <span>开机时间：{{ item.onTime }}</span>
              <span>{{ item.runningTime }}</span>
            </p>
          </div>
        </li>
      </div>
    </n-scrollbar>
    <Empty v-else />
  </div>
</template>

<script setup lang="ts">
import Empty from '@/components/empty-electric/index.vue';
import firea from '@/assets/image/001-a.png';
import fire from '@/assets/image/001.png';
import warna from '@/assets/image/002-a.png';
import warn from '@/assets/image/002.png';
import faulta from '@/assets/image/003-a.png';
import fault from '@/assets/image/003.png';
import riska from '@/assets/image/004-a.png';
import risk from '@/assets/image/004.png';
import $API from '@/common/api';
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useUserInfo } from '@/store';

import defaultPng from '../../staging/assets/default.png';
const userInfo = useUserInfo();
const tableData = ref([]);
const cardObj: any = ref({});
function toPage() {
  router.push('/dianqhjc/zuoyjc-electric_gas_welding');
}
function getData() {
  $API
    .post({
      url: 'electric-gas-service/homeOverview/pageRunningWorkList',
      data: {
        unitId: userInfo.value.unitId,
      },
    })
    .then(async (res: any) => {
      // console.log(res, 'res<><<><><<');
      tableData.value = res.data.rows;
    });
  $API
    .get({
      url: 'electric-gas-service/homeOverview/getRunningAndWarnPreNum',
      params: {
        unitId: userInfo.value.unitId,
      },
    })
    .then(async (res: any) => {
      console.log(res, 'res<><<><><<');
      cardObj.value = res.data;
    });
}
getData();
function getApiBase() {
  // return window.location.hostname == 'localhost'`
  //   ? 'http://*************:9862'
  //   : window.location.origin;
  if (window.location.hostname == 'localhost') {
    // return 'http://*************:9862';
    // return 'https://agjp.tanzervas.com/aqsc/v1';
    return 'https://test-bw.gsafetycloud.com';
  } else if (window.location.hostname == 'agjp.tanzervas.com') {
    return window.location.origin + '/aqsc/v1/dqh';
  } else {
    return window.location.origin;
  }
}
const bgObj = {
  1: risk,
  2: warn,
  3: fault,
  4: fire,
};
const aBgObj = {
  1: riska,
  2: warna,
  3: faulta,
  4: firea,
};

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  list: {
    type: Array as any,
    default: () => [],
  },
  info: {
    type: Array as any,
    default: () => [],
  },
  active: {
    type: String,
    default: '1',
  },
  infoActive: {
    type: Number,
    default: 0,
  },
});
const emits = defineEmits(['tabChange']);
const router = useRouter();
const current = computed(() => {
  return props.active;
});
const name = ref('作业中');
function handleClick(str: any) {
  name.value = str;
  if (str === '作业中') {
    $API
      .post({
        url: 'electric-gas-service/homeOverview/pageRunningWorkList',
        data: {
          unitId: userInfo.value.unitId,
        },
      })
      .then(async (res: any) => {
        // console.log(res, 'res<><<><><<');
        tableData.value = res.data.rows;
      });
  } else {
    $API
      .post({
        url: 'electric-gas-service/homeOverview/pageWarnList',
        data: {
          unitId: userInfo.value.unitId,
        },
      })
      .then(async (res: any) => {
        // console.log(res, 'res<><<><><<');
        tableData.value = res.data.rows;
      });
  }
}
const curTabs = ref(props.infoActive);

function changeTab(item: any, index: number) {
  console.log(item, 'allala');
  curTabs.value = index;
  emits('tabChange', item);
}

const goDetail = async (val) => {
  if (val.eventId) {
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'iot_monitoring',
        userId: userInfo.value.id,
      },
    });

    let url = `${window.$SYS_CFG.parentHostPath}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${val.eventType}&eventId=${val.eventId}&page=/monitor/realTimeMonitorPage`;
    window.open(url, '_blank');
  }
};
const changeType = (val) => {
  // current.value = val.type
  emits('tabChange', val);
};
const getContentText = (val) => {
  //
  if (val.messageContent || val.strongMsgContent)
    return val.messageContent + val.strongMsgContent + '，请控制安全风险。';
  return (
    val.buildingName +
    val.floorName +
    val.deviceAddress +
    val.deviceTypeName +
    '上报了' +
    val.description +
    '请及时前往查看, 请控制安全风险!'
  );
};

async function goMore() {
  if (props.title === '待办任务') {
    router.push({ name: 'notifyTask', state: { tab: '1' } });
  } else {
    // router.push({ name: 'notifyTask', state: { tab: '2' } })
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'iot_monitoring',
        userId: userInfo.value.id,
      },
    });

    let url = `${window.$SYS_CFG.parentHostPath}/ehs-internet-monitor/#/index?token=${res.data.token}&sysCode=iot_monitoring&eventType=${current.value}&page=/monitor/realTimeMonitorPage`;
    window.open(url, '_blank');
  }
}

// 读取消息
function readTask(item) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/readMessage`,
      params: {
        messageId: item.messageId,
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
      }
    });
}

function jump(item) {
  readTask(item);
  if (!item.routePath) {
    return;
  }
  if (item.messageTitle === '隐患复查') {
    ElMessage.warning('请前往移动端-隐患排查治理-隐患复查中处理');
    return;
  }
  let goUrl;
  $API
    .post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: item.sysCode,
        userId: userInfo.value.id,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        if (item.sysCode === 'scene_manage') {
          // 现场管理
          const path = item.routePath.split('#')[1];
          // 移除开头的所有/
          const cleanPath = path.replace(/^\/+/, '');
          goUrl =
            item.routePath.split('#')[0] + '?token=' + res.data.token + '&sysCode=' + item.sysCode + '#/' + cleanPath;
        } else if (
          item.sysCode === 'risk_level' ||
          item.sysCode === 'hazard_inves' ||
          item.sysCode === 'safe-operation'
        ) {
          // 风险管控
          if (item.routePath.includes('?')) {
            goUrl = item.routePath + '&token=' + res.data.token + '&sysCode=' + item.sysCode;
          } else {
            goUrl = item.routePath + '?token=' + res.data.token + '&sysCode=' + item.sysCode;
          }
        } else {
          goUrl = item.routePath + '?token=' + res.data.token + '&sysCode=' + item.sysCode;
        }

        window.open(goUrl, '_blank');
      }
    });
}

function getImg(item) {
  const type = item.type;
  if (current.value == type) return aBgObj[type];
  return bgObj[type];
}

onMounted(() => {
  console.log('🚀 ~ onMounted ~ props.list:', props.list);
});

defineOptions({ name: 'HeadTab' });
</script>

<style scoped lang="scss">
.tasks {
  width: 31%;
  height: 656px;
  background: #ffffff;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  border-radius: 10px;
  padding: 20px;
  .head {
    display: flex;
    justify-content: space-between;
    div:nth-child(1) {
      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 700;
      font-size: 18px;
    }
    div:nth-child(2) {
      color: #527cff;
      cursor: pointer;
    }
  }
  .card {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    div {
      width: 48%;
      height: 74px;
      border-radius: 10px;
      cursor: pointer;
    }
    div:nth-child(1) {
      background: rgb(213, 212, 212);
      p {
        margin-left: 15px;
        font-size: 16px;
        color: #ffffff;
      }
      p:nth-child(1) {
        margin-top: 10px;
        // margin-left: 10px;
      }
    }
    div:nth-child(2) {
      background: rgb(213, 212, 212);
      p {
        margin-left: 15px;
        font-size: 16px;
        color: #ffffff;
      }
      p:nth-child(1) {
        margin-top: 10px;
        // margin-left: 10px;
      }
    }
    .active {
      background: linear-gradient(90deg, #5f85ff 1%, #87a4ff 100%) !important;
      p {
        margin-left: 15px;
        font-size: 16px;
        color: #ffffff;
      }
      p:nth-child(1) {
        margin-top: 10px;
        // margin-left: 10px;
      }
    }
  }
  .list {
    list-style: none;
    cursor: pointer;
    li {
      // width: 350px;
      padding: 8px;
      margin-bottom: 5px;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #dfe6f2;
      // height: 94px;
      display: flex;

      .left {
        width: 80px;
        // background-color: red;
      }
      .right {
        width: 400px;

        // background-color: yellow;
        position: relative;
        // margin-left: 15px;
        p {
          font-size: 14px;
        }
        p:nth-child(1) {
          margin-top: 8px;

          span {
            color: #527cff;
          }
        }

        p:nth-child(2) {
          margin-top: 5px;
          span:nth-child(1) {
            color: #666666;
          }
          span:nth-child(2) {
            position: absolute;
            display: inline-block;
            // width: 160px;
            padding: 2px;

            // height: 17px;
            background: #e5ecff;
            border-radius: 3px 3px 3px 3px;
            color: #4b73ed;
            // text-align: center;
            margin-left: 5px;
          }
        }
      }
      // div:nth-child(2) {
      //   position: relative;
      //   margin-left: 15px;
      //   p {
      //     font-size: 14px;
      //   }
      //   p:nth-child(1) {
      //     margin-top: 8px;

      //     span {
      //       color: #527cff;
      //     }
      //   }

      //   p:nth-child(2) {
      //     margin-top: 5px;
      //     span:nth-child(1) {
      //       color: #666666;
      //     }
      //     span:nth-child(2) {
      //       position: absolute;
      //       display: inline-block;
      //       width: 160px;

      //       height: 17px;
      //       background: #e5ecff;
      //       border-radius: 3px 3px 3px 3px;
      //       color: #4b73ed;
      //       text-align: center;
      //       margin-left: 20px;
      //     }
      //   }
      // }
    }
  }
  .list2 {
    list-style: none;
    cursor: pointer;
    li {
      padding: 8px;
      margin-bottom: 5px;
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #dfe6f2;
      // height: 94px;
      display: flex;
      .left {
        width: 15%;
      }
      .right {
        position: relative;
        margin-left: 15px;
        p {
          font-size: 14px;
        }
        p:nth-child(1) {
          background: rgb(198, 196, 196);
          width: 100px;
          height: 20px;
          color: white;
          text-align: center;
          line-height: 20px;
        }
        p:nth-child(2) {
          margin-top: 8px;

          span {
          }
        }
        p:nth-child(2) {
          span {
          }
        }
        p:nth-child(3) {
          margin-top: 5px;
          span:nth-child(1) {
            color: #666666;
          }
          span:nth-child(2) {
            position: absolute;
            display: inline-block;
            width: 160px;

            height: 17px;
            background: #e5ecff;
            border-radius: 3px 3px 3px 3px;
            color: #4b73ed;
            text-align: center;
            margin-left: 20px;
          }
        }
      }
    }
  }
}
</style>
