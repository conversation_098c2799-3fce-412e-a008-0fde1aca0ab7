<template>
  <el-scrollbar :height="windowSize.height.value + 90 + 'px'">
    <div class="staging-page-main">
      <TopInfo />
      <!-- <div class="notice w-full">
        <div class="notice-name">公告</div>
        <div class="mt-6px">
          <div class="notice-title" @click="goMore">
            {{ topNotice.messageTitle }}
          </div>
        </div>
      </div> -->
      <div class="quick-entrance">
        <div class="quick-entrance-title">
          <img src="./assets/icon.png" alt="" />
          <span>快捷入口</span>
        </div>
        <div class="entrance-container">
          <div
            class="entrance-li"
            v-for="(item, i) in entranceData"
            :key="i"
            @click="handleClick(item)"
            @mousemove="open(item)"
          >
            <div class="lg">
              <img :src="item.icon" alt="" v-if="item.icon !== ''" />
              <div v-else></div>
            </div>
            <div class="rg">
              <div class="rg-title">{{ item.title }}</div>
              <div class="rg-subTitle">{{ item.subTitle }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom mt-24px">
        <ToDoTask />
        <ToDoTask2 />
        <ToDoTask3 />
      </div>
    </div>
    <a-i-robot></a-i-robot>
  </el-scrollbar>

  <el-dialog v-model="robotShow2" @cancel="handleCancel">
    <div v-if="robotShow2">
      <img src="./assets/kefu.png" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import $API from '@/common/api';
import HeadTab from '@/components/SYheadTab/index.vue';
import { useWindowSize } from '@vueuse/core';
import { ElMessage } from 'element-plus';
import { onMounted, ref } from 'vue';
import { useUserInfo } from '@/store';
import aqjypx from './assets/aqjypx.png';
import person from './assets/person.png';
import dianqihan from './assets/dianqihan.png';
import homework from './assets/homework.png';
import cheng from './assets/cheng.png';
import ToDoTask from './comp/ToDoTask.vue';
import ToDoTask2 from './comp/ToDoTask2.vue';
import ToDoTask3 from './comp/ToDoTask3.vue';
import TopInfo from './comp/TopInfo.vue';
import { changeTabArr1 } from './constant';

import CryptoJS from 'crypto-js';
import { decode } from 'js-base64';
import { useRouter } from 'vue-router';
import AIRobot from '@/view/components/AIRobot/index.vue';
const robotShow2 = ref<boolean>(false);
const open = (obj: any) => {};
const handleCancel = (obj: any) => {
  robotShow2.value = false;
};
const entranceData = [
  {
    title: '安全人员管理',
    // subTitle: '风险辨识清单',
    icon: person,
    routerName: '/anqzzyrypb/renygl-org_person_web',
  },
  {
    title: '电气焊监测',
    // subTitle: '待开放',
    icon: dianqihan,
    routerName: '/dianqhjc/yujjc-electric_gas_welding',
  },
  {
    title: '安全作业管理',
    // subTitle: '任务管理',
    icon: homework,
    routerName: '/anqzygl/woddb-safe-operation',
  },
  {
    title: '承包商管理',
    // subTitle: '隐患整改',
    icon: cheng,
    routerName: '/chengbsgl/chengbsllgl-inter_web/qiyllgl-inter_web',
  },

  {
    title: '风险分级管控',
    subTitle: '待开放',
    icon: '',
  },
  {
    title: '隐患排查治理',
    subTitle: '待开放',
    icon: '',
  },
  {
    title: '安全教育培训',
    subTitle: '待开放',
    icon: '',
  },
];

const url = ref();
const router = useRouter();
const windowSize = useWindowSize();
const applyList = ref<any>([]);
const changeTabList = ref(changeTabArr1);
const ui: any = useUserInfo();
const topNotice = ref<any>({});
const token = ref<string>('');
function handleClick(data: any) {
  if (data.subTitle && data.subTitle === '待开放') {
    robotShow2.value = true;
  } else {
    router.push({ path: data.routerName });
  }
}
const imageModules = import.meta.glob('./assets/*.png', { eager: true });

const appIconMap = {};
Object.keys(imageModules).forEach((path) => {
  const fileName = path.replace('./assets/', '').replace('.png', '');
  appIconMap[fileName] = (imageModules[path] as { default: string }).default;
});

const taskList = ref<Array<any>>([]);
const eventList = ref<Array<any>>([]);
const taskInfo = ref<any>([]);
const eventInfo = ref<any>([]);
const changeTabs = ref<any>([]);
const robotShow = ref<boolean>(false);

const active = ref<string>('1');

// 点击切换代办任务方法
function tabChangeTask(item) {
  if (item.messageType) {
    getTaskList(item.messageType);
  }
}

// 获取待办消息列表
function getTaskList(messageType) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/queryMessagePageList`,
      params: {
        pageNo: 1,
        pageSize: 2,
        messageType: messageType,
        messageCategory: '1',
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        taskInfo.value = res.data.rows;
      }
    });
}

// 获取tab第一行分类
function getFirstTab() {
  changeTabs.value = [];
  changeTabs.value.push({ label: '全部', value: '0' });
  $API
    .get({
      // url: `ehs-clnt-platform-service/workbench/msg/querySysTypeList`,
      url: `ehs-clnt-platform-service/workbench/msg/querySysTypeListWithAuth`,
      params: {
        type: 'platform_type',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        res.data.forEach((item: { paramValue: string; paramCode: string }) => {
          changeTabs.value.push({
            label: item.paramValue,
            value: String(item.paramCode),
          });
        });
        changeTab(changeTabs.value[0]);
      }
    });
}

function getApplyList(arr: any) {
  arr.forEach((item: any) => {
    applyList.value.push({
      applyName: item.sysName,
      iconUrl: getAppIcon(item.sysRemark),
      url: item.indexRoute,
      indexRoute: item.indexRoute,
      sysCode: item.sysCode,
    });
  });
}

// 获取第二行应用
function getAppList(list: any, index: string) {
  applyList.value = [];
  // if (changeTabs.value.length >= index) {
  getApplyList(list);
  // } else {
  //   console.error('Invalid index:', index)
  // }
}

// 返回应用图标
function getAppIcon(item: string) {
  return appIconMap[item] || aqjypx;
}

// 获取tab下的应用分类
function changeTab(item: { label: string; value: string; applyList: any }) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/querySystemList`,
      params: {
        type: item.value === '0' ? '' : Number(item.value),
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        getAppList(res.data, item.value);
      }
    });
}

// 切换事件
const tabChange = (val: any) => {
  active.value = val.type;
  getEventInfo(val.type);
};

// 获取事件消息列表
const getEventInfo = async (type = 1) => {
  const res = await $API.post({
    url: `ehs-clnt-internetMonitor-service/monitor/getRealEventPage`,
    params: {
      eventType: type,
      orderFields: 'iotReceiveTime:desc',
      pageNo: 1,
      pageSize: 2,
      orgCode: ui.value.unitId,
    },
  });
  eventInfo.value = res.data.rows;
};

const getEventListDiv = async () => {
  const list: any = [
    {
      bg: 'fire',
      name: '火警',
      typeName: '火警',
      unit: '起',
      count: 0,
      type: 1,
      flag: 'alarmNum',
      isActiveTab: true,
      class: 'cursor-pointer',
    },
    {
      bg: 'warning',
      name: '预警',
      unit: '起',
      count: 0,
      type: 2,
      flag: 'warningNum',
      isActiveTab: false,
      class: 'cursor-pointer',
    },
    {
      bg: 'fault',
      name: '故障',
      unit: '处',
      count: 0,
      type: 3,
      flag: 'faultNum',
      isActiveTab: false,
      class: 'cursor-pointer',
    },
    {
      bg: 'hidden-danger',
      name: '隐患',
      unit: '处',
      count: 0,
      type: 4,
      flag: 'hazardNum',
      isActiveTab: false,
      class: 'cursor-pointer',
    },
  ];
  const params = {
    orgCode: ui.value.unitId,
  };
  const res = await $API.post({
    url: `ehs-clnt-internetMonitor-service/unit/monitor/getRealtimeAlarm`,
    params: {
      ...params,
    },
  });
  list.forEach((i) => {
    i.num = res.data[i.flag];
  });
  let _arr = list.filter((i) => i.num > 0);
  if (_arr.length > 0) {
    tabChange(_arr[0]);
  }
  eventList.value = list;
};

function getListDiv(messageCategory: string) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/queryMessageGroupMessageType`,
      params: {
        messageCategory,
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        if (messageCategory === '1') {
          // 待办事项
          taskList.value = res.data;
          if (res.data[0] && res.data[0].messageType) {
            getTaskList(res.data[0].messageType);
          }
        } else {
          // 事件监测
          eventList.value = res.data;
        }
      }
    });
}

// 获取指定数量消息列表
function getTopNotice(messageCategory: string, pageNo: number, pageSize: number) {
  return new Promise((resolve, reject) => {
    $API
      .get({
        url: `ehs-clnt-platform-service/workbench/msg/queryMessageList`,
        params: {
          messageCategory,
          messageClass: '2',
          pageNo,
          pageSize,
        },
      })
      .then((res: any) => {
        // 置顶公告
        if (messageCategory === '3') {
          topNotice.value = res.data![0] || {};
          resolve(res.data);
        } else if (messageCategory === '1') {
          // 待办事项
          taskInfo.value = res.data;
          resolve(res.data);
        } else {
          // 事件监测
          eventList.value = res.data;
          eventInfo.value = res.data;
          resolve(res.data);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
}

function jump(item: any) {
  let tokenId = ui.value.userToken || ui.value.token;
  if (!item.indexRoute) return ElMessage.success('即将上线...');
  let goUrl;
  $API
    .post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: item.sysCode,
        userId: ui.value.id,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        goUrl = item.indexRoute + '?token=' + res.data.token + '&sysCode=' + item.sysCode;
        window.open(goUrl, '_blank');
      }
    });
}

function encrypt(word) {
  const key = CryptoJS.enc.Utf8.parse(decode('d2ViYSE5MzE5c2RmMmE0QA=='));
  const srcs = CryptoJS.enc.Utf8.parse(word);
  try {
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString();
  } catch (error) {
    return word;
  }
}

function getToken() {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: `ehs-clnt-platform-service/login/serviceLogin`,
        params: {
          telPhone: ui.value.userTelphone,
        },
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          token.value = res.data;
          resolve(res.data);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
}

async function clickRobot() {
  if (y.value === initialY.value) {
    await getToken();
    robotShow.value = true;
    const link = `${ui.value.userTelphone}fck_cq${token.value}fck_cqsingle`;
    url.value = `https://test-bw.gsafetycloud.com/ai-chat-aqsc-web/#/chat?fck_cq='${encrypt(link)}'`;
  }
}

// 读取消息
function readTask(item) {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/readMessage`,
      params: {
        messageId: item.messageId,
        messageClass: '2',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
      }
    });
}

function goMore() {
  readTask(topNotice.value);
  router.push({
    name: 'noticeDetail',
    query: {
      id: topNotice.value.id,
      messageId: topNotice.value.messageId,
    },
  });
}

// 用于存储元素X和Y位置的响应性引用
const x = ref<number>(0);
const y = ref<number>(600);
const initialMouseX = ref<number>(0);
const initialMouseY = ref<number>(0);
const initialX = ref<number>(0);
const initialY = ref<number>(0);

// 是否正在拖动的标志
const isDragging = ref(false);

// 开始拖动的函数
const startDrag = (event) => {
  event.stopPropagation();
  // 记录初始鼠标位置
  // initialMouseX.value = event.clientX
  initialMouseY.value = event.clientY;

  // 记录初始元素位置
  // initialX.value = x.value
  initialY.value = y.value;

  // 开始拖动，设置为true
  isDragging.value = true;

  // 添加鼠标移动和释放时的事件监听器
  document.addEventListener('mousemove', dragging);
  document.addEventListener('mouseup', stopDrag);
};
// 鼠标释放时停止拖动的函数
const stopDrag = (event) => {
  event.stopPropagation();
  // 结束拖动，设置为false
  isDragging.value = false;
  // 移除事件监听器
  document.removeEventListener('mousemove', dragging);
  document.removeEventListener('mouseup', stopDrag);
};
// 当鼠标移动时执行的函数
const dragging = (moveEvent) => {
  moveEvent.stopPropagation();
  // 只有在拖动时才执行
  if (isDragging.value) {
    // 计算鼠标移动的距离
    // const deltaX = moveEvent.clientX - initialMouseX.value
    const deltaY = moveEvent.clientY - initialMouseY.value;

    // 更新元素的位置
    // x.value = initialX.value + deltaX
    y.value = initialY.value + deltaY;
  }
};

onMounted(async () => {
  // 置顶公告
  await getTopNotice('3', 1, 1);
  // 前四条消息
  // await getTopNotice('1', 1, 4)
  // await getTopNotice('2', 1, 2)
  // 通知
  getListDiv('1');

  // 事件
  getEventListDiv();
  // getListDiv('2')
  getFirstTab();
});

defineOptions({ name: 'StagingPage' });
</script>

<style lang="scss" scoped>
.staging-page-main {
  width: 100%;
  padding: 16px 24px 24px 24px;
  background: url('@/assets/image/staging-bg.png') no-repeat;
  background-size: 100% 100%;
  background-color: rgba(200, 213, 255, 1);

  .notice {
    height: 66px;
    margin-top: 24px;
    background: url('@/assets/image/notice-bg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    padding-left: 7%;
    align-items: center;

    .notice-name {
      font-family: YouShe, sans-serif;
      font-weight: 400;
      font-size: 30px;
      color: #527cff;
      line-height: 33px;
      text-align: left;
      margin-right: 28px;
    }

    .notice-title {
      font-weight: 400;
      font-size: 14px;
      color: #527cff;
      line-height: 20px;
      text-align: left;
      margin-bottom: 6px;
      cursor: pointer;
      width: 1000px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .notice-content {
      font-weight: 400;
      font-size: 14px;
      color: #5c5e66;
      line-height: 20px;
    }
  }
  .quick-entrance {
    width: 100%;
    height: 100%;
    margin-top: 20px;
    .quick-entrance-title {
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 20px;
      color: #222222;
      margin-bottom: 14px;
      img {
        margin-right: 10px;
        width: 24px;
        height: 24px;
      }
    }
    .entrance-container {
      width: 100%;
      height: 88px;
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 27px;
      .entrance-li {
        width: 100%;
        height: 88px;
        background: #ffffff;
        box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.08);
        border-radius: 10px 10px 10px 10px;
        display: flex;
        align-items: center;
        padding: 16px;
        cursor: pointer;

        .lg {
          width: 42px;
          height: 42px;
          margin: auto 0;
          margin-right: 12px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .rg {
          .rg-title {
            font-weight: 600;
            font-size: 14px;
            color: #262626;
            margin-bottom: 6px;
          }
          .rg-subTitle {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
          }
        }
      }
    }
  }
  .middle {
    margin-top: 23px;
    background: #ffffff;
    border-radius: 4px;

    .apply-List {
      height: calc(100% - 92px);
      padding-left: 20px;
      margin-top: 27px;
      display: grid;
      grid-template-columns: repeat(10, 1fr);
      justify-items: center;
      row-gap: 16px;
      overflow: hidden;
      overflow-y: auto;

      .item-all {
        cursor: pointer;
      }

      .item {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .item-text {
        font-weight: 400;
        font-size: 16px;
        color: #4d4d4d;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .bottom {
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 850px;
  }

  .underline {
    height: 1px;
    background: #ebeef5;
  }
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 62px;
  height: 62px;
  border-radius: 6px 6px 6px 6px;
  background: rgba(235, 241, 252, 0.65);
}

.item-icon1 {
  background: rgba(235, 241, 252, 0.65);

  img {
    width: 45px;
    height: 44px;
  }
}

.item-icon2 {
  background: rgba(250, 242, 232, 0.65);

  img {
    width: 44px;
    height: 44px;
  }
}

.item-img {
  display: flex;
  align-items: center;
  justify-content: center;
}

.intelligentAssistant {
  width: 100px;
  height: 100px;
  position: fixed;
  top: 50%;
  right: 0;
  background: url('@/assets/image/robot-bj.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;

  .robot {
    width: 100%;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 24px;
    position: absolute;
    bottom: 5px;
    text-align: center;
  }
}

.not {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100% - 48px);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);

  .not-description {
    font-weight: 400;
    font-size: 14px;
    color: #969799;
    line-height: 22px;
    text-align: center;
  }
}

.draggable {
  width: 550px;
  height: 550px;
  border: 1px solid #000;
  position: fixed;
  cursor: grab;
  /* 设置鼠标样式为可抓取状态 */
  z-index: 100;
}
</style>
