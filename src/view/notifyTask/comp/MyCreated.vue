<template>
  <div class="h-full todo-task w-full">
    <el-card class="todo-right">
      <div class="todo-table">
        <div v-if="noticeList.length" class="table-container">
          <el-scrollbar height="100%">
            <el-table v-loading="loading" :data="noticeList" class="flex-1">
              <el-table-column prop="messageTitle" label="通知名称" show-overflow-tooltip>
                <template #default="scope">
                  <div class="title-with-status">
                    <div class="message-title" @click="goDetail(scope.row)" :title="scope.row.messageTitle">
                      {{
                        scope.row.messageTitle.length > 50
                          ? scope.row.messageTitle.slice(0, 50) + '...'
                          : scope.row.messageTitle
                      }}
                    </div>
                    <!-- <div v-if="scope.row.readed !== 0" class="read-status-tag read-tag">已读</div>
                    <div v-else class="read-status-tag unread-tag">未读</div> -->
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="pushTime" label="发布时间" width="280" />
              <el-table-column prop="createUserName" label="创建人" width="220" />
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="goDetail(scope.row)">详情</el-button>
                  <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>

          <div class="mt-[16px]" v-if="total">
            <el-pagination
              v-model:current-page="curPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              layout="total, prev, pager, next, sizes, jumper"
              class="flex justify-end"
            />
          </div>
        </div>

        <div v-else class="empty-container">
          <div class="empty-content">
            <el-image :src="defaultPng" />
            <div class="not-description">暂无通知公告</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>

  <!-- 删除弹框 -->
  <el-dialog v-model="deleteVisible" width="488" height="290" @close="deleteClose">
    <template #header>
      <div class="flex items-center">
        <div class="img"></div>
        <div class="header ml-10px">删除公告</div>
      </div>
    </template>
    <div class="pl-[30px] pr-[30px] !mt-[20px] delete"></div>
    <div class="text-center text-info">确定要删除吗？</div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteClose">取消</el-button>
        <el-button type="primary" @click="deleteSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import $API from '@/common/api';
import { useRouter } from 'vue-router';
import defaultPng from '../../staging/assets/default.png';
import { ElMessage } from 'element-plus';
// import { readReport } from '../../staging/comp/noticeBar/featchData';

const router = useRouter();
const noticeList = ref<any>([]);
const total = ref<number>(0);
const curPage = ref<number>(1);
const pageSize = ref<number>(10);
const pageNo = ref<number>(1);
const selectIndex = ref<string>('');
const deleteId = ref<string>('');
const deleteVisible = ref<boolean>(false);
const loading = ref<boolean>(false);

function handleSizeChange(val: number) {
  pageSize.value = val;
  getEventList();
}

function handleCurrentChange(val: number) {
  pageNo.value = val;
  getEventList();
}

// 获取公告列表
function getEventList() {
  loading.value = true;
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/myMessageList`,
      params: {
        messageClass: '2',
        pageNo: pageNo.value,
        pageSize: pageSize.value,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        noticeList.value = res.data.rows;
        total.value = res.data.total;
        pageNo.value = res.data.pageNo;
        pageSize.value = res.data.pageSize;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function goDetail(item: any) {
  // await readReport(item);
  router.push({
    name: 'noticeDetail',
    query: {
      id: item.id,
      messageId: item.messageId,
      fromTab: '3', // 标识从"我创建的通知"tab进入
    },
  });
}

function handleDelete(item: any) {
  deleteId.value = item.id;
  deleteVisible.value = true;
}

function handleCommand(command: string, index: number) {
  console.log('command -----> 🚀', command);
  if (command === 'edit') {
    // 获取试题详情
    router.push({
      name: 'issueReport',
      query: {
        id: noticeList.value[index].id,
        messageId: noticeList.value[index].messageId,
      },
    });
  } else if (command === 'delete') {
    // 拿到试卷id
    deleteId.value = noticeList.value[index].id;
    deleteVisible.value = true;
  }
}

// 删除确认
function deleteSubmit() {
  $API
    .get({
      url: 'ehs-clnt-platform-service/workbench/msg/delMyMessage',
      params: {
        noticeId: deleteId.value,
      },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        ElMessage.success('操作成功!');
        deleteVisible.value = false;
        getEventList();
      }
    });
}

// 关闭删除弹框
function deleteClose() {
  deleteVisible.value = false;
}

onMounted(() => {
  getEventList();
});
</script>

<style scoped lang="scss">
.todo-task {
  display: flex;
  background-color: #eef7ff;
  border-radius: 0 0 0 10px;

  .todo-right {
    flex: 1;
    position: relative;
    height: calc(100vh - 170px);
    border: none;
    background-color: #eef7ff;

    .todo-table {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .table-container {
      display: grid;
      height: 100%;
      grid-template-rows: 1fr 50px;
    }

    .empty-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;

      .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }

    :deep(.el-card__body) {
      height: 100%;
      position: relative;
    }

    :deep(.el-table) {
      background-color: #eef7ff;

      th {
        background-color: #b7c4f1 !important;
        color: #333;
        font-weight: 500;
        height: 50px;
      }

      .el-table__body {
        tr {
          background-color: #eef7ff;

          &:nth-child(even) {
            background-color: #dfecfb;
          }
        }
      }
    }

    .title-with-status {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .message-title {
      cursor: pointer;
      font-weight: 600;
      font-size: 14px;
      color: #222222;
      line-height: 20px;

      &:hover {
        color: #337eff;
        text-decoration: underline;
      }
    }
    .not-description {
      font-weight: 400;
      font-size: 14px;
      color: #969799;
      line-height: 22px;
      text-align: center;
    }
  }
}

.img {
  width: 18px;
  height: 12px;
  background: url('@/assets/image/drawer_bg.png') no-repeat;
  background-size: 100% 100%;
}

.delete {
  width: 72px;
  height: 72px;
  background: #d9dde8;
  margin: 0 auto;
  background: url('@/assets/image/exam-delete.png') no-repeat center;
  background-size: 100% 100%;
}
</style>
