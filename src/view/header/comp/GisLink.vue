<template>
  <div class="gis-link-comp" v-auth="['platform:gisLink']">
    <img :src="IconGisLink1" alt="" @click="openGisPage" />
  </div>
</template>

<script setup lang="ts">
import IconGisLink1 from '../assets/icon-gis-link_1.png';
import { getPermObj } from '@/store/perms/util.ts';

// gis链接跳转
function openGisPage() {
  const perms = getPermObj('platform:gisLink');
  if (perms && perms.resUrl) window.open(perms.resUrl, '_blank');
}

defineOptions({ name: 'GisLinkComp' });
</script>

<style scoped lang="scss">
.gis-link-comp {
  img {
    @apply w-[36px] h-[36px] cursor-pointer;
  }
}
</style>
