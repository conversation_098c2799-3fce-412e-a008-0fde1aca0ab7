<template>
  <card-comp title="安全看板" card-id="aqkb" :icon="dbrw">
    <template #right>
      <div class="tab-box">
        <div
          :class="[curTab === item.value ? 'active' : '', 'tab-li']"
          v-for="(item, i) in tabList"
          :key="i"
          @click="handleTab(item.value)"
        >
          {{ item.label }}
        </div>
      </div>
    </template>
    <template #content>
      <div class="card-wrapper">
        <n-scrollbar>
          <div v-if="curTab === '1'">
            <MyData />
          </div>
          <div v-else>
            <div
              v-if="
                curQx.includes('risk_level') ||
                curQx.includes('hazard_inves') ||
                curQx.includes('edu') ||
                curQx.includes('inter_web')
              "
            >
              <!--双重预防-->
              <DoublePrevention :curQx="curQx" v-if="curQx.includes('risk_level') || curQx.includes('hazard_inves')" />
              <!--安全培训-->
              <SafetyTraining v-if="curQx.includes('edu')" />
              <!--承包商-->
              <Contractors v-if="curQx.includes('inter_web')" />
            </div>
            <Empty class="mt-[20%]" v-else />
          </div>
        </n-scrollbar>
      </div>
    </template>
  </card-comp>
</template>

<script setup lang="ts">
import DoublePrevention from './comp/doublePrevention.vue';
import SafetyTraining from './comp/safetyTraining.vue';
import Contractors from './comp/contractors.vue';
import CardComp from '@/view/staging/card/common/card.vue';
import dbrw from '@/view/staging/card/common/assets/dbrw.png';
import { ref } from 'vue';
import MyData from './comp/MyData.vue';
import $API from '@/common/api';
import Empty from '@/components/empty/index.vue';

const tabList = [
  { label: '我的数据', value: '1' },
  { label: '单位数据', value: '2' },
];

const curTab = ref('1');

function handleTab(val: string) {
  curTab.value = val;
}

// 权限限制风险 risk_level
// 权限限制隐患 hazard_inves
// 权限限制安全培训 edu
// 权限限制承包商 inter_web

const curQx = ref<string[]>([]);
function getqxsj() {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/querySystemList`,
      params: {
        type: '',
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        const resolveData = res.data || [];
        if (resolveData.length > 0) {
          curQx.value = resolveData.map((item: any) => item.sysCode);
        }
      }
    });
}

getqxsj();

defineOptions({ name: 'SafetyDashboard' });
</script>

<style scoped lang="scss">
.tab-box {
  @apply w-[160px] h-[32px] flex mr-[16px];

  .tab-li {
    @apply w-[80px] h-full flex justify-center items-center cursor-pointer text-[#323232];
    border: 1px solid #e5e5e5;
    &.active {
      color: #fff;
      background: #527cff;
    }
  }
}
.card-wrapper {
  @apply bg-[#fff] pl-[16px] pr-[16px] pt-[10px] h-full;
  border-radius: 8px;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.08);
}
</style>
