<template>
  <div class="contractors">
    <ComHeaderA title="承包商" class="mb-[10px]" />
    <div class="data-grid mb-[10px]">
      <div class="grid-item" v-for="(item, i) in listData" :key="i">
        <span>{{ item.value }}</span>
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import $API from '@/common/api';
import ComHeaderA from '@/components/HeadTitle/comTitleA.vue';
import { ref } from 'vue';
import { useUserInfo } from '~/store';

const userInfo = useUserInfo();
const listData = ref<{ label: string; value: number }[]>([
  {
    label: '企业数',
    value: 0,
  },
  {
    label: '承包商人数',
    value: 0,
  },
  {
    label: '服务项目',
    value: 0,
  },
  {
    label: '在途项目',
    value: 0,
  },
]);
function getData() {
  $API
    .get({
      url: `edu-api-server/api/workbench/datastatistics/totalRelateCount`,
      params: {
        userId: userInfo.value.id,
      },
    })
    .then((res) => {
      const resData = res.data || {};
      listData.value[0].value = resData.qys || 0;
      listData.value[1].value = resData.cbsrs || 0;
      listData.value[2].value = resData.cbsfwxms || 0;
      listData.value[3].value = resData.cbsztxms || 0;
    });
}

getData();
defineOptions({ name: 'SafetyDashboardContractors' });
</script>

<style scoped lang="scss">
.contractors {
  .data-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 4等宽列 */
    grid-template-rows: auto; /* 1行 */
    gap: 10px;

    .grid-item {
      @apply flex flex-col justify-center items-center text-[#323232];
      height: 56px;
      background: #f0f5fa;
      border-radius: 4px 4px 4px 4px;
      font-size: 14px;

      span {
        &:first-child {
          @apply mb-[6px];
        }
      }
    }
  }
}
</style>
