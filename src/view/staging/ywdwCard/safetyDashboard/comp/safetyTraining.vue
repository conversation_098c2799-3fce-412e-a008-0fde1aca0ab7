<template>
  <div class="safety-training">
    <ComHeaderA title="安全培训" class="mb-[10px]" />
    <div class="data-grid">
      <div class="grid-item !bg-[#F0F5FA] !text-[#0080FF]">
        <span>{{ eduData.planTotalCount }}</span>
        <span>计划总数</span>
      </div>
      <div class="grid-item !bg-[#EEF7F4] !text-[#00AD74]">
        <span>{{ eduData.totalTaskCount }}</span>
        <span>任务总数</span>
      </div>
      <div class="grid-item !bg-[#EEF7F4] !text-[#00AD74]">
        <span>{{ eduData.dealTaskCount }}</span>
        <span>已实施任务</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ComHeaderA from '@/components/HeadTitle/comTitleA.vue';
import { ref } from 'vue';
import $API from '~/common/api';

// 教育培训数据
const eduData = ref({
  planTotalCount: '0', // 计划总数
  totalTaskCount: '0', // 任务总数
  dealTaskCount: '0', // 已实施任务
});

function getEduData() {
  $API
    .get({
      url: `train-server/api/workbench/getTrainPlanTaskStatistics`,
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        eduData.value = res.data;
      }
    });
}

getEduData();
defineOptions({ name: 'SafetyDashboardSafetyTraining' });
</script>

<style scoped lang="scss">
.safety-training {
  .data-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3等宽列 */
    grid-template-rows: auto; /* 1行 */
    gap: 10px;
    margin-bottom: 18px;

    .grid-item {
      @apply flex flex-col justify-center items-center text-[#323232];
      height: 56px;
      background: #f0f5fa;
      border-radius: 4px;
      font-size: 14px;

      span {
        &:first-child {
          @apply mb-[6px];
        }
      }
    }
  }
}
</style>
