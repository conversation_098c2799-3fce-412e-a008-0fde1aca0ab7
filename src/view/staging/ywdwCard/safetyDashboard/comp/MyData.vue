<template>
  <div class="my-data" v-if="myDataList.length > 0">
    <div class="my-data-li" v-for="(item, i) in myDataList" :key="i">
      <div class="li-title">
        <div class="title">{{ item.syName }}</div>
        <div class="to-more" @click="handleMore(item)">查看更多</div>
      </div>
      <div class="li-main">
        <div class="grid-item" v-for="(child, ip) in item.pieGraphVoList" :key="ip">
          <span class="item-bold">{{ child.value }}</span>
          <span>{{ child.name }}</span>
        </div>
      </div>
    </div>
  </div>
  <Empty class="mt-[20%]" v-else />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import $API from '~/common/api';
import { ElMessage } from 'element-plus';
import { useUserInfo } from '@/store';
import Empty from '@/components/empty/index.vue';

const myDataList = ref<any[]>([]);
function getMyData() {
  $API
    .get({
      url: `ehs-clnt-platform-service/workbench/msg/aqkbMy`,
      params: {
        sysClass: '1',
      },
    })
    .then((res: any) => {
      myDataList.value = res.data || [];
    });
}

function handleMore(item: any) {
  if (!item.indexRoute) return ElMessage.success('暂无数据...');
  const status =
    !item.indexRoute.includes('agjp.tanzervas.com') && window.location.origin.includes('agjp.tanzervas.com');
  if (status) {
    window.open(item.indexRoute, '_blank');
    return;
  }
  let goUrl;
  $API
    .post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: item.syCode,
        userId: useUserInfo().value.id,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        goUrl = item.indexRoute + '?token=' + res.data.token + '&sysCode=' + item.syCode;
        window.open(goUrl, '_blank');
      }
    });
}

getMyData();
defineOptions({ name: 'MyData' });
</script>

<style scoped lang="scss">
.my-data {
  @apply w-full h-full flex flex-col gap-[10px];
  .my-data-li {
    @apply w-full min-h-[127px] flex flex-col p-[12px];
    background: #fafbfc;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #f1f2f5;
    .li-title {
      @apply w-full h-[40px] flex justify-between items-center mb-[10px];
      .title {
        @apply text-[#333333] font-bold;
      }
      .to-more {
        @apply text-[#999999] cursor-pointer;
      }
    }
    .li-main {
      @apply w-full flex-1 grid  grid-cols-3 gap-[16px];
      .grid-item {
        @apply flex flex-col justify-center items-center text-[#323232] text-[14px];

        .item-bold {
          @apply font-bold;
        }

        span {
          &:first-child {
            @apply mb-[10px];
          }
        }
      }
    }
  }
}
</style>
