<template>
  <div class="double-prevention">
    <ComHeaderA title="双重预防" class="mb-[10px]" />
    <div class="flex items-center w-full mb-[10px]" v-if="curQx.includes('risk_level')">
      <div class="flex-1 h-[180px]">
        <DoughnutChart :echartsData="echartsData" :color="color" :extra="option" />
      </div>
      <div class="w-[244px]">
        <div class="text-[14px] leading-[20px] mb-[6px]">风险辨识情况</div>
        <div class="data-grid w-full h-[58px] mb-[12px]">
          <div class="grid-item">
            <span class="text-[#F11D1D]">{{ riskProfile.riskIdentificationStatusUndo }}</span>
            <span>未辨识</span>
          </div>
          <div class="grid-item">
            <span class="text-[#0080FF]">{{ riskProfile.riskIdentificationStatusDoing }}</span>
            <span>辨识中</span>
          </div>
          <div class="grid-item">
            <span class="text-[#00AD74]">{{ riskProfile.riskIdentificationStatusDone }}</span>
            <span>已辨识</span>
          </div>
        </div>
        <!--        <div class="text-[14px] leading-[20px] mb-[6px]">管控情况</div>-->
        <!--        <div class="data-grid w-full h-[58px]">-->
        <!--          <div class="grid-item">-->
        <!--            <span class="text-[#F11D1D]">{{ riskProfile.controlStatusN }}</span>-->
        <!--            <span>未开始</span>-->
        <!--          </div>-->
        <!--          <div class="grid-item">-->
        <!--            <span class="text-[#0080FF]">{{ riskProfile.controlStatusY }}</span>-->
        <!--            <span>管控中</span>-->
        <!--          </div>-->
        <!--        </div>-->
      </div>
    </div>
    <div class="w-full h-[127px] flex gap-[10px]" v-if="curQx.includes('hazard_inves')">
      <div class="data-grid h-full w-[108px]">
        <div class="grid-item">
          <span class="!mb-[16px]">{{ taskData.taskAllNum }}</span>
          <span>隐患排查任务</span>
        </div>
      </div>
      <div class="data-grid h-full w-[108px]">
        <div class="grid-item">
          <span class="!mb-[16px]">{{ taskData.taskEdNum }}</span>
          <span>已完成任务</span>
        </div>
      </div>
      <div class="data-grid h-full flex-1 flex-wrap">
        <div class="grid-item w-1/2">
          <span>{{ hazardData.totalNum }}</span>
          <span>隐患上报</span>
        </div>
        <div class="grid-item w-1/2">
          <span>{{ hazardData.undisposeNum }}</span>
          <span>待整改</span>
        </div>
        <div class="grid-item w-1/2">
          <span>{{ hazardData.overdueNum }}</span>
          <span>超期隐患</span>
        </div>
        <div class="grid-item w-1/2">
          <span>{{ hazardData.disposedRate }}%</span>
          <span>整改率</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComHeaderA from '@/components/HeadTitle/comTitleA.vue';
import DoughnutChart from './doughnutChart.vue';
import $API from '@/common/api';
import { useUserInfo } from '@/store';

const props = defineProps<{
  curQx: string[];
}>();

interface Data {
  name: string;
  value: number;
}
const ui = useUserInfo().value;
const color = [
  ['#D9001B', '#D9001B'],
  ['#E87108', '#E87108'],
  ['#FFDD00', '#FFDD00'],
  ['#527CFF', '#527CFF'],
];
const option = {
  legend: { bottom: '5%' },
  series: {
    radius: ['60%', '90%'],
    center: ['50%', '50%'],
  },
  title: {
    show: true,
    text: 0,
    top: 'center',
    left: 'center',
    textStyle: {
      color: '#333639',
      textAlign: 'center',
      fontSize: 20,
      fontWeight: 'bold',
    },
  },
};
const echartsData = ref<Data[]>([
  { name: '重大风险', value: 0 },
  { name: '较大风险', value: 0 },
  { name: '一般风险', value: 0 },
  { name: '低风险', value: 0 },
]);

// 风险图表数据
function getChartData() {
  $API
    .get({
      url: `ehs-clnt-rmc-service/indexDataStatistics/getRiskLevelCount`,
      params: {
        unitId: ui.unitId,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        echartsData.value[0].value = res.data.riskLevel1;
        echartsData.value[1].value = res.data.riskLevel2;
        echartsData.value[2].value = res.data.riskLevel3;
        echartsData.value[3].value = res.data.riskLevel4;
        option.title.text = echartsData.value.reduce((sum, cur) => sum + cur.value, 0);
      }
    });
}

// 风险情况
const riskProfile = ref({
  // 风险辨识情况
  riskIdentificationStatusUndo: '0', //-未辨识
  riskIdentificationStatusDoing: '0', //-辨识中
  riskIdentificationStatusDone: '0', //-已辨识
  // 风险管控情况
  controlStatusN: '0', //-未开始
  controlStatusY: '0', //-管控中
});
// 风险情况数据
function getRiskProfile() {
  $API
    .get({
      url: `ehs-clnt-rmc-service/indexDataStatistics/getRiskIdentificationCount`,
      params: {
        unitId: ui.unitId,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        riskProfile.value = res.data;
      }
    });
}

// 排查任务数据
const taskData = ref({
  taskAllNum: '0', // 隐患排查任务
  taskEdNum: '0', // 已完成任务
});

function getOrgTree() {
  $API
    .post({
      url: `ehs-clnt-hazard-service/ehsUpms/getOrgTree`,
      params: {
        orgCode: ui.unitId,
        type: '2',
        needChildUnit: '1',
        unitStatus: '1',
      },
    })
    .then((res: any) => {
      if (res && res.code == '200' && res.data[0]) {
        getTaskData(res.data[0].levelCode);
      }
    });
}
function getTaskData(levelCode: string) {
  $API
    .post({
      url: `ehs-clnt-hazard-service/hazardPlanStatistic/taskAllStatistic`,
      data: {
        levelCode: levelCode,
        levelCodes: null,
        unitId: ui.unitId,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        taskData.value = res.data;
      }
    });
}

// 隐患数据
const hazardData = ref({
  totalNum: 0, // 隐患上报数
  undisposeNum: 0, // 待整改数
  overdueNum: 0, // 超期隐患数
  disposedRate: 0, // 整改率
  disnosingNum: 0,
});

function getHazardData() {
  $API
    .post({
      url: `ehs-clnt-hazard-service/screen/getTopStastics`,
      params: {
        unitId: ui.unitId,
      },
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        hazardData.value = res.data;
      }
    });
}

getChartData();
getRiskProfile();
getHazardData();
getOrgTree();

defineOptions({ name: 'SafetyDashboardDoublePrevention' });
</script>

<style scoped lang="scss">
.double-prevention {
  @apply mb-[16px];

  .data-grid {
    @apply flex items-center justify-around;
    background: #fafbfc;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #f1f2f5;

    .grid-item {
      @apply flex flex-col justify-center items-center text-[#323232];
      font-size: 14px;

      span {
        &:first-child {
          @apply mb-[6px];
        }
      }
    }
  }
}
</style>
