<template>
  <div class="warning-dashboard">
    <div class="type">
      <div class="item" v-for="item of typeList" :key="item.name">
        <div
          class="num"
          :class="['num-' + String(item.num).length, 'num-' + item.key]"
          :style="`background: url('${IconMap[item.key]}') center / 100% no-repeat`"
        >
          {{ item.num }}
        </div>
        <span class="name">{{ item.name }}</span>
      </div>
    </div>
    <el-scrollbar class="list">
      <template v-if="listData.length">
        <div class="item" v-for="(item, index) in listData" :key="index" @click="toDetail(item)">
          <img class="icon" :src="IconMap[item.iconKey]" alt="" />
          <div>
            <div class="content">{{ item.content }}</div>
            <div class="time">{{ item.time }}</div>
          </div>
        </div>
      </template>
      <Empty v-else />
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import $API from '~/common/api';
import Empty from '@/components/empty/index.vue';
import { IconMap } from './Icon';
import { IGetRealtimeAlarm, IGetAlarmPageList, IAlarmListItem } from './type';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useUserInfo } from '~/store';

const router = useRouter();
const userInfo = useUserInfo();
const typeList = ref([
  { key: 'hj', num: 0, name: '火警' },
  { key: 'yj', num: 0, name: '预警' },
  { key: 'gz', num: 0, name: '故障' },
  { key: 'yh', num: 0, name: '隐患' },
  // { key: 'lx', num: 0, name: '离线' },
]);
const listData = ref<IAlarmListItem[]>([]);

const toDetail = async (item: any) => {
  if (item.rawData.disposeId) {
    const res = await $API.post({
      url: `ehs-clnt-platform-service/login/checkSysPower`,
      data: {
        sysCode: 'iot_monitoring',
        userId: userInfo.value.id,
      },
    });

    const url = `${window.$SYS_CFG.parentHostPath}/ehs-internet-monitor/#/monitor/eventManagement?token=${res.data.token}&sysCode=iot_monitoring&eventType=${item.rawData.eventType}&disposeId=${item.rawData.disposeId}`;
    window.open(url, '_blank');
  }
};

function getData() {
  $API
    .get({
      url: `ehs-clnt-platform-service/totalstatistics/ywMonitorEventStatistics`,
      params: {
        unitId: userInfo.value.unitId,
        unitName: userInfo.value.unitName,
      },
    })
    .then((res) => {
      const data = (res.data || {}) as any;
      typeList.value[0].num = data.hjsl || 0;
      typeList.value[1].num = data.yjsl || 0;
      typeList.value[2].num = data.gzsl || 0;
      typeList.value[3].num = data.yhsl || 0;
      // typeList.value[4].num = data.offlineNum || 0;
      // 注意：API 还返回了 actionNum 和 shieldNum，但当前页面上没有对应的显示项
    });

  $API
    .post({
      url: `/ehs-clnt-platform-service/mis/getAlarmPageList`,
      params: {
        orgCode: userInfo.value.orgCode,
        pageSize: 5,
        // unitId: userInfo.value.unitId,
      },
    })
    .then((res: any) => {
      const data = res.data || [];
      // 处理列表数据
      listData.value = data.map((item: IGetAlarmPageList) => {
        let content = '';
        let iconKey = 'hj_s';

        // 获取字段，处理可能的undefined情况
        const buildingName = item.buildingName || '未知建筑';
        const floorName = item.floorName || '';
        const deviceAddress = item.deviceAddress || '未知位置';
        const deviceTypeName = item.deviceTypeName || '未知设备';
        const eventDesc = item.eventDesc || '未知事件';

        // 根据类型构建不同的内容文本
        if (item.type === '火警') {
          content = `位于${buildingName}${floorName}${deviceAddress}的${deviceTypeName}上报了${eventDesc},请及时前往查看！`;
          iconKey = 'hj_s';
        } else if (item.type === '预警') {
          content = `位于${buildingName}${floorName}${deviceAddress}的${deviceTypeName}上报了${eventDesc},请及时前往查看！`;
          iconKey = 'yj_s';
        } else if (item.type === '故障') {
          content = `位于${buildingName}${floorName}${deviceAddress}的${deviceTypeName}发生了${eventDesc},请及时前往查看！`;
          iconKey = 'gz_s';
        } else if (item.type === '隐患') {
          if (item.hazardSource === 1) {
            content = `${buildingName}${floorName}${deviceAddress}发现了${eventDesc}隐患,请及时处置！`;
          } else {
            content = `${item.hazardSourceName || '未知来源'}发现了${item.hazardDesc || eventDesc},请及时处置！`;
          }
          iconKey = 'yh_s';
        }
        // else if (item.type === '离线') {
        //   content = `位于${buildingName}${floorName}${deviceAddress}的${deviceTypeName}已离线,请及时检查！`;
        //   iconKey = 'lx_s';
        // }

        return {
          content,
          time: item.lastEventTime || '',
          type: item.type || '未知类型',
          iconKey,
          rawData: item, // 原始数据
        };
      });
    });
}

// init
getData();

defineOptions({ name: 'HomeStagingWarningDashboard' });
</script>

<style scoped lang="scss">
.warning-dashboard {
  height: 470px;
  background: linear-gradient(180deg, #f1f5ff 0%, rgba(255, 255, 255, 0) 46%), #ffffff;
  box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  border-radius: 6px;
  padding: 20px;
  display: grid;
  grid-template-rows: 110px 1fr;
  background: #fff !important;

  .type {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;

    .item {
      text-align: center;
      .num {
        width: 74px;
        height: 74px;
        line-height: 74px;
        border-radius: 100%;
        color: #fff;
        font-size: 18px;
        font-weight: bold;

        &-4 {
          font-size: 15px;
        }
        &-5 {
          font-size: 12px;
        }
        &-6 {
          font-size: 11px;
        }
        &-7,
        &-8,
        &-9 {
          font-size: 10px;
        }
      }
      .name {
        display: inline-block;
        color: #222;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }

  .list {
    .item {
      width: 100%;
      min-height: 88px;
      border: 1px solid #dfe6f2;
      border-radius: 10px;
      display: grid;
      grid-template-columns: auto 1fr;
      align-items: center;
      column-gap: 12px;
      padding: 15px;
      margin-bottom: 15px;
      cursor: pointer;
      transition: border-color 0.3s;

      &:hover {
        border-color: rgba(82, 124, 255, 0.6);
      }
      &:active {
        border-color: rgba(82, 124, 255, 0.9);
      }

      .icon {
        width: 36px;
        height: 36px;
      }

      .content {
        color: #222;
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        margin-bottom: 5px;
      }
      .time {
        color: #666;
      }
    }
  }
}
</style>
