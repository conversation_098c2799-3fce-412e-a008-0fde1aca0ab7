export interface IGetRealtimeAlarm {
  alarmNum: number;
  warningNum: number;
  faultNum: number;
  hazardNum: number;
  actionNum: number;
  offlineNum: number;
  shieldNum: number;
}

export interface IAlarmListItem {
  content: string;
  time: string;
  type: string;
  iconKey: string;
  rawData: Record<string, any>;
}

export interface IGetAlarmPageList {
  eventSourceId: string;
  fireStateStr: string;
  operationState: number;
  operationStateStr: string;
  floorId: string;
  reachState: number;
  eventDesc: string;
  eventType: number;
  buildingName: string;
  floorName: string;
  deviceAddress: string;
  deviceTypeName: string;
  lastEventTime: string;
  type: string;
  hazardSource?: number;
  hazardSourceName?: string;
  hazardDesc?: string;
}
