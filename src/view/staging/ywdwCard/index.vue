<template>
  <n-grid :x-gap="20" :y-gap="20" :cols="3" layout-shift-disabled>
    <n-gi v-for="(Component, index) in cardComponents" :key="index">
      <component :is="Component" />
    </n-gi>
  </n-grid>
</template>

<script setup lang="ts">
import CardC0 from '../card/C0/index.vue';
import safetyDashboard from './safetyDashboard/index.vue';
import warningDashboard from './warningDashboard/index.vue';

const cardComponents = [CardC0, warningDashboard, safetyDashboard];

defineOptions({ name: 'StagingCardIndexComp' });
</script>

<style scoped lang="scss"></style>
