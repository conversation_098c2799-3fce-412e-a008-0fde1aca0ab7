<template>
  <div
    class="intelligentAssistant"
    @click="clickRobot"
    :style="{ right: `${x}px`, top: `${y}px` }"
    @mousedown="startDrag"
    @mousemove="dragging"
    @mouseleave="stopDrag"
  >
    <div class="flex mt-[11.5px]">
      <img class="w-[68px] m-auto robot-img" :src="robot" alt="robot" />
    </div>
    <div class="robot">安全智能助手</div>
  </div>

  <!-- 智能机器人 -->
  <popup-side v-model="robotShow" popupTitle="智能助手" width="900px">
    <iframe :src="url" class="w-full h-full" />
  </popup-side>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import $API from '@/common/api.ts';
import { useUserInfo } from '@/store';
import PopupSide from '@/components/public/popup/popupSide.vue';
import robot from '@/assets/image/robot.gif';

const ui: any = useUserInfo();
const url = ref();
const token = ref<string>('');
// 用于存储元素X和Y位置的响应性引用
const x = ref<number>(0);
const y = ref<number>(600);
const initialMouseY = ref<number>(0);
const initialY = ref<number>(0);
// 是否正在拖动的标志
const isDragging = ref(false);
const robotShow = ref<boolean>(false);

async function clickRobot() {
  if (y.value === initialY.value) {
    await getToken();
    robotShow.value = true;
    url.value = `${window.$SYS_CFG.VITE_AI_URL}?token=${token.value}&phone=${ui.value.userTelphone}&userName=${ui.value.userName}`;
  }
}

// 开始拖动的函数
const startDrag = (event: any) => {
  event.stopPropagation();
  // 记录初始鼠标位置
  initialMouseY.value = event.clientY;
  // 记录初始元素位置
  initialY.value = y.value;
  // 开始拖动，设置为true
  isDragging.value = true;
  // 添加鼠标移动和释放时的事件监听器
  document.addEventListener('mousemove', dragging);
  document.addEventListener('mouseup', stopDrag);
};

// 鼠标释放时停止拖动的函数
const stopDrag = (event: any) => {
  event.stopPropagation();
  // 结束拖动，设置为false
  isDragging.value = false;
  // 移除事件监听器
  document.removeEventListener('mousemove', dragging);
  document.removeEventListener('mouseup', stopDrag);
};

// 当鼠标移动时执行的函数
const dragging = (moveEvent: any) => {
  moveEvent.stopPropagation();
  // 只有在拖动时才执行
  if (isDragging.value) {
    // 计算鼠标移动的距离
    const deltaY = moveEvent.clientY - initialMouseY.value;
    // 更新元素的位置
    y.value = initialY.value + deltaY;
  }
};

function getToken() {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: `ehs-clnt-platform-service/login/serviceLogin`,
        params: {
          telPhone: ui.value.userTelphone,
          clientType: 'web',
        },
      })
      .then((res: any) => {
        if (res && res.code == 'success') {
          token.value = res.data;
          resolve(res.data);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
}

defineOptions({ name: 'AIRobot' });
</script>

<style scoped lang="scss">
.intelligentAssistant {
  width: 100px;
  height: 100px;
  position: fixed;
  top: 50%;
  right: 0;
  background: url('@/assets/image/robot-bj.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
  z-index: 999;

  .robot {
    width: 100%;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 24px;
    position: absolute;
    bottom: 5px;
    text-align: center;
  }
}

.robot-img {
  -webkit-user-drag: none;
}
</style>
