<!--
 * @Author: xginger <EMAIL>
 * @Date: 2025-06-18 10:33:28
 * @LastEditors: xginger <EMAIL>
 * @LastEditTime: 2025-06-18 10:33:43
 * @FilePath: \platform\src\components\empty\empty2.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-empty :class="$style['empty']" :theme-overrides="overrideEmptyTheme()">
    <template #image>
      <img src="@/assets/image/empty2.png" alt="no data" />
    </template>
  </el-empty>
</template>

<script lang="ts" setup>
import { overrideEmptyTheme } from './emptyTheme';

defineOptions({ name: 'ComWidgetEmpty' });
</script>

<style module lang="scss">
.empty {
  @apply h-full justify-center;
}
</style>
